import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  CreditCard,
  Edit,
  PlusCircle,
  Trash2,
  BanknoteIcon,
  Loader2,
  Save,
  Plus,
  TrendingUp,
  Settings
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import orderService, { Order, OrderItem } from "@/services/orderService";
import * as cardService from "@/services/cardService";
import { CardSwipe } from "@/services/cardService";
import * as posService from "@/services/posService";
import * as moneyTransferService from "@/services/moneyTransferService";
import { MoneyTransfer } from "@/services/moneyTransferService";
import apiClient from "@/lib/apiClient";
import * as moneyReturnService from "@/services/moneyReturnService";
import * as bankService from "@/services/bankService";
import { formatDateTimeVN } from "@/utils/date-format";
import { CreateOrderButton } from "./CreateOrderButton";
import { ProfitAdjustmentModal } from "./ProfitAdjustmentModal";
import { BankFeeAdjustmentModal } from "./BankFeeAdjustmentModal";

// Định nghĩa styles cho các trạng thái đơn hàng
const statusStyles: Record<string, { color: string; bgColor: string; borderColor: string; label: string }> = {
  "pending": {
    color: "text-slate-500",
    bgColor: "bg-slate-500/10",
    borderColor: "border-slate-500/20",
    label: "Chờ xử lý"
  },
  "processing": {
    color: "text-amber-500",
    bgColor: "bg-amber-500/10",
    borderColor: "border-amber-500/20",
    label: "Đang xử lý"
  },
  "completed": {
    color: "text-emerald-500",
    bgColor: "bg-emerald-500/10",
    borderColor: "border-emerald-500/20",
    label: "Hoàn thành"
  },
  "cancelled": {
    color: "text-rose-500",
    bgColor: "bg-rose-500/10",
    borderColor: "border-rose-500/20",
    label: "Đã hủy"
  },
};

// Enhanced interfaces to fix type errors
interface Bank {
  id: number;
  name: string;
}

interface Card {
  id: number;
  card_number: string;
  bank_id: number;
  customer_id: number;
  Bank?: Bank;
  Customer?: {
    id: number;
    name: string;
  };
}

interface OrderDetailProps {
  orderId: string | number;
  onBack?: () => void; // Make onBack optional
  onEdit: (id: number) => void;
  onDataChange?: () => void; // Callback khi dữ liệu thay đổi
}

export function OrderDetail({ orderId, onBack, onEdit, onDataChange }: OrderDetailProps) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orderData, setOrderData] = useState<Order | null>(null);

  // Thêm state cho dialog quẹt thẻ
  const [showCardSwipeDialog, setShowCardSwipeDialog] = useState(false);
  const [cardSwipeForm, setCardSwipeForm] = useState({
    id: '',
    amount: '',
    pos: '',
    bankFee: '',
    note: '',
    cardNumber: ''
  });

  // State để lưu trữ mã quẹt thẻ tiếp theo
  const [nextCardSwipeId, setNextCardSwipeId] = useState<number>(1);

  // State để lưu trữ mã chuyển tiền tiếp theo
  const [nextMoneyTransferId, setNextMoneyTransferId] = useState<number>(1);

  // Thêm state cho dialog chuyển tiền
  const [showMoneyTransferDialog, setShowMoneyTransferDialog] = useState(false);
  const [moneyTransferForm, setMoneyTransferForm] = useState({
    id: '',
    amount: '',
    note: ''
  });

  // State cho card swipes và money transfers
  const [cardSwipes, setCardSwipes] = useState<CardSwipe[]>([]);
  const [loadingCardSwipes, setLoadingCardSwipes] = useState(false);

  // State cho danh sách POS
  const [posList, setPosList] = useState<any[]>([]);
  const [loadingPosList, setLoadingPosList] = useState(false);

  const [moneyTransfers, setMoneyTransfers] = useState<MoneyTransfer[]>([]);
  const [loadingMoneyTransfers, setLoadingMoneyTransfers] = useState(false);

  // State để lưu trữ tổng số tiền đã quẹt trong đơn hàng
  const [totalSwipedAmount, setTotalSwipedAmount] = useState<number>(0);

  // State cho dialog chỉnh sửa đơn hàng
  const [showEditOrderDialog, setShowEditOrderDialog] = useState(false);
  const [orderEditForm, setOrderEditForm] = useState({
    id: '',
    amount: '',
    fee: '',
    customerFee: '',
    negativeAmount: '',
    debtDeduction: '',
    note: ''
  });
  const [editLoading, setEditLoading] = useState(false);

  // State để đánh dấu đã xử lý đơn hàng hoàn thành
  const [processedCompletedOrder, setProcessedCompletedOrder] = useState<boolean>(false);

  // Không cần state cho modal tạo đơn hàng mới nữa vì đã được xử lý trong CreateOrderButton

  // Thêm state cho danh sách ngân hàng
  const [bankList, setBankList] = useState<any[]>([]);
  const [loadingBankList, setLoadingBankList] = useState(false);

  // State cho modal điều chỉnh lợi nhuận
  const [showProfitAdjustmentModal, setShowProfitAdjustmentModal] = useState(false);

  // State cho modal điều chỉnh phí bank
  const [showBankFeeAdjustmentModal, setShowBankFeeAdjustmentModal] = useState(false);
  const [selectedCardSwipe, setSelectedCardSwipe] = useState<CardSwipe | null>(null);

  // Hàm tính tổng số tiền đã quẹt
  const calculateTotalSwipedAmount = (swipes: CardSwipe[]) => {
    return swipes.reduce((total, swipe) => total + Number(swipe.amount || 0), 0);
  };

  // Các hàm tính toán đã được loại bỏ để sử dụng trực tiếp giá trị từ database
  // Điều này đảm bảo tính nhất quán giữa frontend và database

  // Hàm lấy danh sách chuyển tiền
  const fetchMoneyTransfers = async () => {
    if (!orderId) return;

    setLoadingMoneyTransfers(true);
    try {
      // Lấy danh sách chuyển tiền của đơn hàng
      const response = await moneyTransferService.getMoneyTransfersByOrder(Number(orderId));
      console.log('Money transfers data:', response);

      // Xử lý dữ liệu trả về
      let transfersData;
      if (response && Array.isArray(response)) {
        transfersData = response;
      } else if (response && response.data && Array.isArray(response.data)) {
        transfersData = response.data;
      } else {
        transfersData = [];
      }

      setMoneyTransfers(transfersData || []);

      // Tính toán mã chuyển tiền tiếp theo bằng cách lấy ID lớn nhất từ tất cả chuyển tiền
      try {
        // Lấy tất cả chuyển tiền từ hệ thống
        const allTransfersResponse = await apiClient.get('/api/transfers');
        console.log('All transfers response:', allTransfersResponse);

        // Xử lý dữ liệu trả về
        let allTransfersData;
        if (allTransfersResponse.data && Array.isArray(allTransfersResponse.data)) {
          allTransfersData = allTransfersResponse.data;
        } else if (allTransfersResponse.data && allTransfersResponse.data.data && Array.isArray(allTransfersResponse.data.data)) {
          allTransfersData = allTransfersResponse.data.data;
        } else {
          allTransfersData = [];
        }

        // Lấy tất cả ID và tìm ID lớn nhất
        const allTransferIds = allTransfersData.map(transfer => {
          const id = typeof transfer.id === 'string' ? parseInt(transfer.id, 10) : Number(transfer.id);
          return isNaN(id) ? 0 : id;
        });

        console.log('All money transfer IDs:', allTransferIds);

        if (allTransferIds.length > 0) {
          const maxId = Math.max(...allTransferIds);
          console.log('Max money transfer ID from all transfers:', maxId);
          setNextMoneyTransferId(maxId + 1);
        } else {
          // Fallback: Sử dụng ID từ chuyển tiền của đơn hàng nếu không lấy được tất cả
          if (transfersData && transfersData.length > 0) {
            const transferIds = transfersData.map(transfer => {
              const id = typeof transfer.id === 'string' ? parseInt(transfer.id, 10) : Number(transfer.id);
              return isNaN(id) ? 0 : id;
            });

            if (transferIds.length > 0) {
              const maxId = Math.max(...transferIds);
              console.log('Fallback - Max money transfer ID from order:', maxId);
              setNextMoneyTransferId(maxId + 1);
            } else {
              setNextMoneyTransferId(1);
            }
          } else {
            setNextMoneyTransferId(1);
          }
        }
      } catch (error) {
        console.error('Error fetching all transfers:', error);

        // Fallback nếu có lỗi khi lấy tất cả chuyển tiền
        if (transfersData && transfersData.length > 0) {
          const transferIds = transfersData.map(transfer => {
            const id = typeof transfer.id === 'string' ? parseInt(transfer.id, 10) : Number(transfer.id);
            return isNaN(id) ? 0 : id;
          });

          if (transferIds.length > 0) {
            const maxId = Math.max(...transferIds);
            console.log('Error fallback - Max money transfer ID:', maxId);
            setNextMoneyTransferId(maxId + 1);
          } else {
            setNextMoneyTransferId(1);
          }
        } else {
          setNextMoneyTransferId(1);
        }
      }
    } catch (error) {
      console.error('Error fetching money transfers:', error);
      toast.error('Không thể tải dữ liệu chuyển tiền');
    } finally {
      setLoadingMoneyTransfers(false);
    }
  };

  // Lấy dữ liệu đơn hàng
  const fetchOrderData = async () => {
    try {
      const order = await orderService.getOrderById(Number(orderId));
      console.log('Dữ liệu đơn hàng từ backend:', order);
      console.log('Giá trị need_to_transfer từ backend:', order?.need_to_transfer);
      setOrderData(order);
    } catch (error) {
      console.error('Lỗi khi lấy dữ liệu đơn hàng:', error);
      toast.error('Không thể tải dữ liệu đơn hàng');
    }
  };

  // Xử lý thêm card swipe
  const handleAddCardSwipe = async (data: any) => {
    try {
      await cardService.createCardSwipe(data);
      toast.success('Đã thêm card swipe thành công');

      // Cập nhật lại trạng thái đơn hàng
      await orderService.updateOrderStatus(Number(orderId));

      // Tải lại dữ liệu
      await fetchOrderData();
      // Tải lại dữ liệu card swipes để cập nhật số tiền cần quẹt
      const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
      setCardSwipes(swipesData || []);
    } catch (error) {
      console.error('Lỗi khi thêm card swipe:', error);
      toast.error('Không thể thêm card swipe. Vui lòng thử lại sau.');
    }
  };

  // Xử lý thêm money transfer
  const handleAddMoneyTransfer = async (data: any) => {
    try {
      await moneyTransferService.createMoneyTransfer(data);
      toast.success('Đã thêm money transfer thành công');

      // Chỉ cập nhật trạng thái đơn hàng một lần thông qua service
      // Điều này sẽ tự động xác định trạng thái đúng dựa trên dữ liệu hiện tại
      await orderService.updateOrderStatus(Number(orderId));

      // Tải lại dữ liệu
      await fetchOrderData();
      // Tải lại dữ liệu money transfers để cập nhật số tiền cần chuyển
      await fetchMoneyTransfers();

      // Gọi callback để thông báo dữ liệu đã thay đổi
      if (onDataChange) onDataChange();
    } catch (error) {
      console.error('Lỗi khi thêm money transfer:', error);
      toast.error('Không thể thêm money transfer. Vui lòng thử lại sau.');
    }
  };

  // Xử lý khi lấy dữ liệu đơn hàng và card swipes
  useEffect(() => {
    const fetchOrderDataAndSwipes = async () => {
      try {
        setLoading(true);
        const data = await orderService.getOrderById(Number(orderId));
        console.log('Raw order data:', data);

        // Không đặt lại processedCompletedOrder khi lần đầu tải dữ liệu
        // để tránh tạo lại money_return
        if (data) {
          // Chỉ đặt processedCompletedOrder = true khi đơn hàng là Rút & đã hoàn thành
          if (data.status === 'completed' && data.order_type === 'Rút') {
            setProcessedCompletedOrder(true);
          }
          setOrderData(data);
        }

        // Sau khi lấy dữ liệu đơn hàng, lấy danh sách card swipes
        setLoadingCardSwipes(true);
        const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
        console.log('Card swipes data:', swipesData);
        setCardSwipes(swipesData || []);

        // Tính tổng số tiền đã quẹt
        const totalSwiped = swipesData ? calculateTotalSwipedAmount(swipesData) : 0;
        setTotalSwipedAmount(totalSwiped);

        // Tính toán mã quẹt thẻ tiếp theo bằng cách lấy ID lớn nhất từ tất cả quẹt thẻ
        try {
          // Lấy tất cả quẹt thẻ từ hệ thống
          const allSwipesResponse = await apiClient.get('/api/swipes');
          console.log('All card swipes response:', allSwipesResponse);

          // Xử lý dữ liệu trả về
          let allSwipesData = [];
          if (allSwipesResponse.data && Array.isArray(allSwipesResponse.data)) {
            allSwipesData = allSwipesResponse.data;
          } else if (allSwipesResponse.data && allSwipesResponse.data.data && Array.isArray(allSwipesResponse.data.data)) {
            allSwipesData = allSwipesResponse.data.data;
          }

          // Lấy tất cả ID và tìm ID lớn nhất
          const allSwipeIds = allSwipesData.map(swipe => {
            const id = typeof swipe.id === 'string' ? parseInt(swipe.id, 10) : Number(swipe.id);
            return isNaN(id) ? 0 : id;
          });

          console.log('All card swipe IDs:', allSwipeIds);

          if (allSwipeIds.length > 0) {
            const maxId = Math.max(...allSwipeIds);
            console.log('Max card swipe ID from all swipes:', maxId);
            setNextCardSwipeId(maxId + 1);
          } else {
            // Fallback: Sử dụng ID từ quẹt thẻ của đơn hàng nếu không lấy được tất cả
            if (swipesData && swipesData.length > 0) {
              const swipeIds = swipesData.map(swipe => {
                const id = typeof swipe.id === 'string' ? parseInt(swipe.id, 10) : Number(swipe.id);
                return isNaN(id) ? 0 : id;
              });

              if (swipeIds.length > 0) {
                const maxId = Math.max(...swipeIds);
                console.log('Fallback - Max card swipe ID from order:', maxId);
                setNextCardSwipeId(maxId + 1);
              } else {
                setNextCardSwipeId(1);
              }
            } else {
              setNextCardSwipeId(1);
            }
          }
        } catch (error) {
          console.error('Error fetching all card swipes:', error);

          // Fallback nếu có lỗi khi lấy tất cả quẹt thẻ
          if (swipesData && swipesData.length > 0) {
            const swipeIds = swipesData.map(swipe => {
              const id = typeof swipe.id === 'string' ? parseInt(swipe.id, 10) : Number(swipe.id);
              return isNaN(id) ? 0 : id;
            });

            if (swipeIds.length > 0) {
              const maxId = Math.max(...swipeIds);
              console.log('Error fallback - Max card swipe ID:', maxId);
              setNextCardSwipeId(maxId + 1);
            } else {
              setNextCardSwipeId(1);
            }
          } else {
            setNextCardSwipeId(1);
          }
        }

        // Lấy danh sách chuyển tiền
        await fetchMoneyTransfers();

        // Lấy danh sách POS
        try {
          setLoadingPosList(true);
          const posData = await posService.getPOSList();
          console.log('POS terminals data:', posData);
          setPosList(posData.data || []);
        } catch (error) {
          console.error('Error fetching POS terminals:', error);
          toast.error('Không thể tải danh sách máy POS');
        } finally {
          setLoadingPosList(false);
        }

        setLoading(false);
        setLoadingCardSwipes(false);
      } catch (error) {
        console.error('Error fetching order data:', error);
        setLoading(false);
        setLoadingCardSwipes(false);
        toast.error('Không thể tải dữ liệu đơn hàng và quẹt thẻ');
      }
    };

    fetchOrderDataAndSwipes();
  }, [orderId]);

  // Cập nhật tổng tiền đã quẹt khi danh sách card swipes thay đổi
  useEffect(() => {
    const totalSwiped = calculateTotalSwipedAmount(cardSwipes);
    setTotalSwipedAmount(totalSwiped);
  }, [cardSwipes]);

  // Trả về style mặc định nếu status không hợp lệ
  const getStatusStyle = (status: string) => {
    const defaultStyle = {
      color: "text-gray-500",
      bgColor: "bg-gray-500/10",
      borderColor: "border-gray-500/20"
    };
    return statusStyles[status as keyof typeof statusStyles] || defaultStyle;
  };

  // Calculate the percentage of completion
  const calculateProgress = (current: string | number, total: string | number) => {
    // Chuyển đổi chuỗi thành số, loại bỏ các ký tự không phải số
    const currentValue = typeof current === 'string' ? parseFloat(current.replace(/[^\d.-]/g, "")) : Number(current);
    const totalValue = typeof total === 'string' ? parseFloat(total.replace(/[^\d.-]/g, "")) : Number(total);

    // Kiểm tra nếu tổng giá trị là 0 hoặc không hợp lệ
    if (!totalValue || totalValue <= 0) return 0;

    // Tính phần trăm, không giới hạn trong khoảng 0-100
    const percentage = (currentValue / totalValue) * 100;
    return Math.round(percentage);
  };

  // Format percentage value
  const formatPercent = (percent: number) => {
    return `${percent}%`;
  };

  // Xử lý khi submit form quẹt thẻ
  const handleSubmitCardSwipe = async () => {
    // Validate form
    if (!cardSwipeForm.amount) {
      toast.error("Vui lòng nhập số tiền");
      return;
    }
    if (!cardSwipeForm.pos) {
      toast.error("Vui lòng chọn máy POS");
      return;
    }

    try {
      // Chuẩn bị dữ liệu để gửi lên API
      // Loại bỏ dấu . phân cách hàng nghìn
      const amount = parseFloat(cardSwipeForm.amount.replace(/\./g, ''));
      const posId = parseInt(cardSwipeForm.pos);

      if (isNaN(amount)) {
        toast.error("Số tiền không hợp lệ");
        return;
      }

      if (!cardSwipeForm.pos) {
        toast.error("Vui lòng chọn máy POS");
        return;
      }

      // Đảm bảo có card_id từ đơn hàng
      if (!orderData?.card?.id) {
        toast.error("Không tìm thấy thông tin thẻ của đơn hàng");
        return;
      }

      const cardSwipeData = {
        order_id: Number(orderId),
        card_id: orderData.card.id,
        swipe_date: new Date().toISOString(),
        amount,
        pos_id: posId,
        note: cardSwipeForm.note,
        user_id: 1 // Giả sử user_id = 1, trong thực tế sẽ lấy từ context hoặc state
      };

      // Hiển thị trạng thái đang tải
      setLoadingCardSwipes(true);

      // Gọi API để lưu dữ liệu
      console.log("Card swipe data being sent:", cardSwipeData);
      const response = await cardService.createCardSwipe(cardSwipeData);
      console.log("Card swipe created response:", response);

      // Kiểm tra response để đảm bảo API call thành công
      if (response) {
        // Tạo một quẹt thẻ mới để hiển thị ngay lập tức (trước khi fetch lại từ server)
        const newSwipe: CardSwipe = {
          id: nextCardSwipeId,
          order_id: Number(orderId),
          card_id: orderData.card.id,
          swipe_date: new Date().toISOString(),
          amount: amount,
          pos_id: posId,
          POS: posList.find(pos => pos.id === posId),
          note: cardSwipeForm.note || '',
          bank_fee: 0, // Giả sử phí bank = 0 trước khi có dữ liệu thật
          net_amount: amount, // Giả sử số tiền thực nhận = amount trước khi có dữ liệu thật
          user_id: 1, // Giả sử user_id = 1
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Cập nhật danh sách quẹt thẻ và tổng số tiền đã quẹt ngay lập tức
        const updatedSwipes = [...cardSwipes, newSwipe];
        setCardSwipes(updatedSwipes);

        // Cập nhật tổng số tiền đã quẹt
        const newTotalSwipedAmount = totalSwipedAmount + amount;
        setTotalSwipedAmount(newTotalSwipedAmount);

        // Đóng dialog và reset form
        setShowCardSwipeDialog(false);

        // Reset form
        setCardSwipeForm({
          id: '',
          amount: '',
          pos: '',
          bankFee: '',
          note: '',
          cardNumber: ''
        });

        // Thông báo thành công
        toast.success("Đã thêm quẹt thẻ thành công");

        // Gọi callback để thông báo dữ liệu đã thay đổi
        if (onDataChange) onDataChange();
      } else {
        // Nếu API trả về lỗi
        toast.error('Không thể thêm quẹt thẻ. Vui lòng thử lại sau.');
        setLoadingCardSwipes(false);
        return; // Ngừng xử lý nếu API call thất bại
      }

      // Sau khi thêm quẹt thẻ thành công, cập nhật trạng thái đơn hàng và tải lại dữ liệu
      try {
        // Cập nhật trạng thái đơn hàng một lần
        await orderService.updateOrderStatus(Number(orderId));

        // Tải lại dữ liệu đơn hàng
        const updatedOrderData = await orderService.getOrderById(Number(orderId));
        setOrderData(updatedOrderData);

        // Tải lại danh sách card swipes
        const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
        setCardSwipes(swipesData || []);

        // Cập nhật tổng số tiền đã quẹt
        if (updatedOrderData && updatedOrderData.total_swiped !== undefined && updatedOrderData.total_swiped !== null) {
          setTotalSwipedAmount(Number(updatedOrderData.total_swiped));
        } else {
          const total = calculateTotalSwipedAmount(swipesData || []);
          setTotalSwipedAmount(total);
        }
      } catch (error) {
        console.error('Error refreshing data after card swipe:', error);
      } finally {
        setLoadingCardSwipes(false);
      }

      // Cập nhật mã quẹt thẻ tiếp theo
      // Nếu response có trả về ID của card swipe mới, sử dụng ID đó + 1
      if (response && response.data && response.data.id) {
        setNextCardSwipeId(Number(response.data.id) + 1);
      } else {
        // Nếu không có ID từ response, tăng ID hiện tại lên 1
        setNextCardSwipeId(nextCardSwipeId + 1);
      }
    } catch (error) {
      console.error('Error creating card swipe:', error);
      toast.error('Không thể thêm quẹt thẻ. Vui lòng thử lại sau.');
      setLoadingCardSwipes(false);
    }
  };

  // Xử lý khi submit form chuyển tiền
  const handleSubmitMoneyTransfer = async () => {
    // Validate form
    if (!moneyTransferForm.amount) {
      toast.error("Vui lòng nhập số tiền");
      return;
    }

    try {
      // Loại bỏ dấu . phân cách hàng nghìn để chuyển thành số
      const amount = parseFloat(moneyTransferForm.amount.replace(/\./g, ''));

      if (isNaN(amount)) {
        toast.error("Số tiền không hợp lệ");
        return;
      }

      // Tạo dữ liệu để gọi API
      const transferData = {
        id: nextMoneyTransferId, // Add id to fix type error
        order_id: Number(orderId),
        amount: amount,
        note: moneyTransferForm.note,
        transfer_date: new Date().toISOString(),
        user_id: 1 // Giả sử user_id = 1, trong thực tế sẽ lấy từ thông tin đăng nhập
      };

      console.log("Saving money transfer for order", orderId, transferData);

      // Gọi API để tạo chuyển tiền mới
      const response = await moneyTransferService.createMoneyTransfer(transferData as MoneyTransfer);
      console.log("Money transfer created response:", response);

      // Thông báo thành công và đóng dialog
      toast.success("Đã thêm chuyển tiền thành công");
      setShowMoneyTransferDialog(false);

      // Cập nhật mã chuyển tiền tiếp theo
      setNextMoneyTransferId(nextMoneyTransferId + 1);

      // Làm mới dữ liệu chuyển tiền và cập nhật trạng thái đơn hàng
      await fetchMoneyTransfers();

      // Cập nhật trạng thái đơn hàng một lần thông qua service
      await orderService.updateOrderStatus(Number(orderId));

      // Tải lại dữ liệu đơn hàng
      await fetchOrderData();
    } catch (error) {
      console.error('Error creating money transfer:', error);
      toast.error('Không thể tạo chuyển tiền mới');
    }
  };

  // Xử lý khi mở dialog thêm quẹt thẻ
  const handleOpenCardSwipeDialog = () => {
    // Lấy số tiền cần quẹt từ database và định dạng
    const needToSwipeAmount = Number(orderData?.need_to_swipe || 0);
    const formattedAmount = needToSwipeAmount > 0 ? formatNumberWithDot(needToSwipeAmount) : '';

    // Điền thông tin tự động vào form
    setCardSwipeForm({
      id: nextCardSwipeId.toString(),
      amount: formattedAmount,
      pos: '',
      bankFee: '',
      note: '',
      cardNumber: orderData?.card?.card_number || ''
    });
    setShowCardSwipeDialog(true);
  };

  // Xử lý thay đổi giá trị trong form quẹt thẻ
  const handleCardSwipeFormChange = (field: string, value: string) => {
    if (field === 'amount') {
      // Loại bỏ các ký tự không phải số
      const numericValue = value.replace(/[^0-9]/g, '');

      // Định dạng số với dấu . phân cách hàng nghìn
      if (numericValue) {
        const formattedValue = parseInt(numericValue, 10).toLocaleString('vi-VN');
        setCardSwipeForm(prev => ({
          ...prev,
          [field]: formattedValue
        }));
      } else {
        setCardSwipeForm(prev => ({
          ...prev,
          [field]: ''
        }));
      }
    } else {
      setCardSwipeForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Xử lý mở dialog thêm chuyển tiền mới
  const handleOpenMoneyTransferDialog = () => {
    // Lấy số tiền cần chuyển từ database và định dạng
    const needToTransferAmount = Number(orderData?.need_to_transfer || 0);
    const formattedAmount = needToTransferAmount > 0 ? formatNumberWithDot(needToTransferAmount) : '';

    // Khởi tạo form với ID tiếp theo
    setMoneyTransferForm({
      id: nextMoneyTransferId.toString(),
      amount: formattedAmount,
      note: ''
    });

    // Mở dialog
    setShowMoneyTransferDialog(true);
  };

  // Xử lý thay đổi giá trị trong form chuyển tiền
  const handleMoneyTransferFormChange = (field: string, value: string) => {
    // Định dạng số tiền với dấu . phân cách hàng nghìn khi nhập
    if (field === 'amount') {
      // Loại bỏ các ký tự không phải số
      const numericValue = value.replace(/[^\d]/g, '');

      // Chuyển thành số và định dạng lại
      if (numericValue) {
        const formattedValue = new Intl.NumberFormat('vi-VN', {
          maximumFractionDigits: 0
        }).format(parseInt(numericValue, 10));

        setMoneyTransferForm(prev => ({
          ...prev,
          [field]: formattedValue
        }));
      } else {
        setMoneyTransferForm(prev => ({
          ...prev,
          [field]: ''
        }));
      }
    } else {
      setMoneyTransferForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Hàm định dạng số với dấu . phân cách hàng nghìn
  const formatNumberWithDot = (value: number | string | undefined): string => {
    if (value === undefined || value === null || value === '') return '';

    // Chuyển giá trị thành số
    let numValue: number;

    if (typeof value === 'string') {
      // Xử lý chuỗi số có thể có dấu thập phân
      const cleanValue = value.replace(/,/g, '.'); // Chuẩn hóa dấu phân cách thập phân
      numValue = parseFloat(cleanValue);
    } else {
      numValue = Number(value);
    }

    if (isNaN(numValue)) return '';

    // Định dạng số với dấu . phân cách hàng nghìn và loại bỏ số thập phân
    return numValue.toLocaleString('vi-VN', {
      maximumFractionDigits: 0 // Không hiển thị phần thập phân
    });
  };

  // Hàm chuyển đổi chuỗi có định dạng thành số
  const parseFormattedNumber = (value: string): number => {
    if (!value) return 0;

    try {
      // Loại bỏ tất cả dấu . phân cách hàng nghìn
      const cleanedValue = value.replace(/\./g, '');

      // Chuyển đổi thành số
      const parsedValue = Number(cleanedValue);

      if (isNaN(parsedValue)) {
        console.error('Giá trị không phải số hợp lệ:', value);
        return 0;
      }

      console.log(`Parsing ${value} -> ${parsedValue}`);
      return parsedValue;
    } catch (error) {
      console.error('Lỗi khi xử lý số:', error);
      return 0;
    }
  };

  // Xử lý thay đổi giá trị trong form chỉnh sửa đơn hàng
  const handleOrderEditFormChange = (field: string, value: string) => {
    // Nếu là trường số, định dạng lại với dấu . phân cách hàng nghìn
    if (['amount', 'customerFee', 'negativeAmount', 'debtDeduction'].includes(field)) {
      // Loại bỏ tất cả các ký tự không phải số
      const numericValue = value.replace(/[^0-9]/g, '');
      if (numericValue === '') {
        setOrderEditForm(prev => ({
          ...prev,
          [field]: ''
        }));
        return;
      }

      // Chuyển thành số và định dạng lại
      const formattedValue = Number(numericValue).toLocaleString('vi-VN', {
        maximumFractionDigits: 0 // Không hiển thị phần thập phân
      });

      const updatedForm = {
        ...orderEditForm,
        [field]: formattedValue
      };

      // Nếu người dùng thay đổi số tiền hoặc tiền phí, tính toán lại giá trị khác
      if (field === 'amount' && updatedForm.fee) {
        // Khi thay đổi số tiền, tính lại tiền phí dựa trên phí %
        const amount = parseFormattedNumber(formattedValue);
        const feePercentage = parseFloat(updatedForm.fee);
        if (!isNaN(feePercentage) && amount > 0) {
          const calculatedFee = (amount * feePercentage) / 100;
          updatedForm.customerFee = calculatedFee.toLocaleString('vi-VN', {
            maximumFractionDigits: 0
          });
        }
      } else if (field === 'customerFee' && updatedForm.amount) {
        // Khi thay đổi tiền phí, tính lại phí %
        const amount = parseFormattedNumber(updatedForm.amount);
        const fee = parseFormattedNumber(formattedValue);
        if (amount > 0) {
          // Lưu giá trị phí % chính xác (không làm tròn) để tính toán
          const calculatedFeePercentage = (fee / amount) * 100;
          // Hiển thị với 2 chữ số thập phân nhưng lưu trữ giá trị chính xác
          updatedForm.fee = calculatedFeePercentage.toFixed(2);
          // Đảm bảo tiền phí chính xác bằng cách tính lại từ số tiền và phí %
          const recalculatedFee = (amount * calculatedFeePercentage) / 100;
          updatedForm.customerFee = recalculatedFee.toLocaleString('vi-VN', {
            maximumFractionDigits: 0
          });
        }
      }

      setOrderEditForm(updatedForm);
    } else if (field === 'fee') {
      // Xử lý riêng cho trường phí % (cho phép số thập phân)
      const numericValue = value.replace(/[^0-9.]/g, '');

      const updatedForm = {
        ...orderEditForm,
        fee: numericValue
      };

      // Nếu có số tiền, tính lại tiền phí dựa trên phí % mới
      if (updatedForm.amount) {
        const amount = parseFormattedNumber(updatedForm.amount);
        const feePercentage = parseFloat(numericValue);
        if (!isNaN(feePercentage) && amount > 0) {
          // Tính toán tiền phí chính xác dựa trên phí % được nhập
          const calculatedFee = (amount * feePercentage) / 100;
          // Làm tròn đến số nguyên để hiển thị
          updatedForm.customerFee = calculatedFee.toLocaleString('vi-VN', {
            maximumFractionDigits: 0
          });

          // Đảm bảo lưu trữ giá trị phí % chính xác
          // Khi gửi lên server, sẽ sử dụng giá trị customerFee thay vì tính từ fee_percentage
        }
      }

      setOrderEditForm(updatedForm);
    } else {
      // Các trường không phải số
      setOrderEditForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Xử lý mở dialog chỉnh sửa đơn hàng
  const handleOpenEditOrderDialog = () => {
    if (!orderData) return;

    // Lấy giá trị số từ dữ liệu đơn hàng
    const totalAmount = typeof orderData.total_amount === 'string'
      ? parseFloat(orderData.total_amount)
      : Number(orderData.total_amount || 0);

    const customerFee = typeof orderData.charge_customer_fee === 'string'
      ? parseFloat(orderData.charge_customer_fee)
      : Number(orderData.charge_customer_fee || 0);

    const negativeAmount = typeof orderData.negative_amount === 'string'
      ? parseFloat(orderData.negative_amount)
      : Number(orderData.negative_amount || 0);

    const debtDeduction = typeof orderData.debt_deduction === 'string'
      ? parseFloat(orderData.debt_deduction)
      : Number(orderData.debt_deduction || 0);

    setOrderEditForm({
      id: orderData.id.toString(),
      amount: formatNumberWithDot(totalAmount) || '',
      fee: orderData.fee_percentage?.toString() || '',
      customerFee: formatNumberWithDot(customerFee) || '',
      negativeAmount: formatNumberWithDot(negativeAmount) || '',
      debtDeduction: formatNumberWithDot(debtDeduction) || '',
      note: orderData.note || ''
    });

    console.log('Form data:', {
      original: orderData.total_amount,
      parsed: totalAmount,
      formatted: formatNumberWithDot(totalAmount)
    });

    setShowEditOrderDialog(true);
  };

  // Xử lý submit form chỉnh sửa đơn hàng
  const handleSubmitOrderEdit = async () => {
    if (!orderData) return;

    // Kiểm tra điều kiện cho đơn hàng "Rút" có giá trị "Trừ Nợ"
    if (orderData.order_type === 'Rút' && orderEditForm.debtDeduction && parseFormattedNumber(orderEditForm.debtDeduction) > 0) {
      // Kiểm tra nếu khách hàng có nợ phí
      try {
        if (orderData.customer && orderData.customer.id) {
          const customerResponse = await apiClient.get(`/api/customers/${orderData.customer.id}`);
          const customer = customerResponse.data.data;

          if (customer.outstanding_fee <= 0) {
            toast.error("Khách hàng không có nợ phí, không thể áp dụng Trừ Nợ");
            return;
          }
        }
      } catch (error) {
        console.error("Lỗi khi kiểm tra thông tin khách hàng:", error);
        toast.error("Không thể kiểm tra thông tin khách hàng");
        return;
      }

      // Kiểm tra debt_deduction <= (total_swiped - charge_customer_fee)
      const totalSwiped = Number(orderData.total_swiped || 0);
      const customerFeeAmount = parseFormattedNumber(orderEditForm.customerFee);
      const debtDeductionAmount = parseFormattedNumber(orderEditForm.debtDeduction);

      if (debtDeductionAmount > (totalSwiped - customerFeeAmount)) {
        toast.error("Số tiền Trừ Nợ không được vượt quá (Tổng đã quẹt - Tiền phí)");
        return;
      }
    }

    try {
      setEditLoading(true);

      // Chuẩn bị dữ liệu để gửi lên API
      const orderUpdateData: Partial<Order> = {
        total_amount: orderEditForm.amount ? parseFormattedNumber(orderEditForm.amount) : orderData.total_amount,
        fee_percentage: orderEditForm.fee ? Number(orderEditForm.fee) : orderData.fee_percentage,
        charge_customer_fee: orderEditForm.customerFee ? parseFormattedNumber(orderEditForm.customerFee).toString() : (orderData.charge_customer_fee ? orderData.charge_customer_fee.toString() : "0"),
        negative_amount: orderEditForm.negativeAmount ? parseFormattedNumber(orderEditForm.negativeAmount).toString() : (orderData.negative_amount ? orderData.negative_amount.toString() : "0"),
        debt_deduction: orderEditForm.debtDeduction ? parseFormattedNumber(orderEditForm.debtDeduction).toString() : (orderData.debt_deduction ? orderData.debt_deduction.toString() : "0"),
        note: orderEditForm.note !== undefined ? orderEditForm.note : orderData.note,
        // Không còn cần order_date vì đã sử dụng created_at thay thế
        status: orderData.status,
        created_at: orderData.created_at,
        updated_at: new Date().toISOString()
      };

      console.log('Order update data:', orderUpdateData);
      console.log('Original order data:', orderData);
      console.log('Form data:', orderEditForm);

      // Gọi API để cập nhật đơn hàng
      const updatedOrder = await orderService.updateOrder(Number(orderData.id), orderUpdateData as Order);
      console.log('Updated order response:', updatedOrder);

      // Đóng dialog và thông báo thành công
      setShowEditOrderDialog(false);
      toast.success('Cập nhật đơn hàng thành công');

      // Cập nhật dữ liệu đơn hàng trực tiếp trong state với dữ liệu trả về từ server
      if (updatedOrder) {
        setOrderData(updatedOrder);
      }

      // Cập nhật trạng thái đơn hàng một lần
      await orderService.updateOrderStatus(Number(orderId));

      // Tải lại tất cả dữ liệu
      console.log('Tải lại dữ liệu đơn hàng sau khi chỉnh sửa');
      const updatedOrderData = await orderService.getOrderById(Number(orderId));
      console.log('Dữ liệu đơn hàng mới:', updatedOrderData);
      setOrderData(updatedOrderData);

      // Cập nhật lại danh sách card swipes và money transfers
      const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
      setCardSwipes(swipesData || []);

      await fetchMoneyTransfers();

      // Kiểm tra giá trị need_to_transfer mới từ backend
      console.log('Giá trị need_to_transfer mới từ backend:', updatedOrderData?.need_to_transfer);

      // Gọi callback để thông báo dữ liệu đã thay đổi
      if (onDataChange) onDataChange();
    } catch (error: any) {
      console.error('Lỗi khi cập nhật đơn hàng:', error);

      // Hiển thị thông báo lỗi từ server nếu có
      if (error.message) {
        toast.error(error.message);
      } else if (error.serverError && error.serverError.message) {
        toast.error(error.serverError.message);
      } else {
        toast.error('Không thể cập nhật đơn hàng. Vui lòng thử lại sau.');
      }
    } finally {
      setEditLoading(false);
    }
  };

  // Thêm hàm để lấy tên ngân hàng từ ID
  const getBankName = (bankId: number | undefined) => {
    return bankService.getBankName(bankId, bankList);
  };

  // Thêm hàm để lấy tên POS từ ID
  const getPOSName = (posId: number | undefined) => {
    if (!posId) return 'Không có thông tin';
    const pos = posList.find(p => p.id === posId);
    return pos ? pos.name : `POS ID: ${posId}`;
  };

  // Đã chuyển hàm updateOrderStatus sang sử dụng trực tiếp từ orderService

  // Đơn hàng "Rút" không tạo money_return và không ảnh hưởng đến outstanding_fee

  // Cập nhật useEffect để gọi updateOrderStatus khi có thay đổi
  // Sử dụng debounce để tránh gọi quá nhiều lần
  useEffect(() => {
    // Luôn gọi updateOrderStatus khi có thay đổi, kể cả khi đơn hàng đã ở trạng thái completed
    if (orderData) {
      // Sử dụng setTimeout để tránh gọi quá nhiều lần
      const timer = setTimeout(() => {
        console.log('Gọi updateOrderStatus từ useEffect sau khi dữ liệu thay đổi');
        orderService.updateOrderStatus(Number(orderId))
          .then(updatedOrder => {
            // Chỉ cập nhật state nếu trạng thái thay đổi
            if (updatedOrder && updatedOrder.status !== orderData.status) {
              console.log('Trạng thái đã thay đổi, cập nhật lại dữ liệu đơn hàng');
              // Đơn hàng "Rút" không tạo money_return và không ảnh hưởng đến outstanding_fee
              fetchOrderData();
            }
          })
          .catch(error => {
            console.error('Lỗi khi cập nhật trạng thái đơn hàng:', error);
          });
      }, 300); // Đợi 300ms để tránh gọi quá nhiều lần

      // Cleanup function để tránh memory leak
      return () => clearTimeout(timer);
    }
  }, [cardSwipes, moneyTransfers, orderData?.total_amount, orderData?.status]);

  // Thêm useEffect để lấy danh sách ngân hàng
  useEffect(() => {
    const fetchBanks = async () => {
      try {
        setLoadingBankList(true);
        const response = await bankService.getBankList();
        if (response.success) {
          console.log('Bank list data:', response.data);
          setBankList(Array.isArray(response.data) ? response.data : []);
        }
      } catch (error) {
        console.error('Error fetching banks:', error);
      } finally {
        setLoadingBankList(false);
      }
    };

    fetchBanks();
  }, []);

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader>
          <div className="h-5 bg-muted rounded w-1/4"></div>
          <div className="h-4 bg-muted rounded w-1/3"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="grid grid-cols-2 gap-4">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Không cần hàm xử lý tạo đơn hàng mới nữa vì đã được xử lý trong CreateOrderButton

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <Button
          variant="ghost"
          onClick={() => {
            navigate('/orders');
            if (onBack) onBack();
          }}
          className="w-full sm:w-auto justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Quay lại
        </Button>
        <div className="flex gap-2 w-full sm:w-auto">
          <CreateOrderButton
            className="flex-1 sm:flex-initial"
            onOrderCreated={(orderId) => {
              // Khi đơn hàng mới được tạo, navigate sẽ được gọi trong OrderCreateModal
              // và sẽ chuyển đến trang chi tiết của đơn hàng mới
            }}
          />
          <Button variant="outline" onClick={handleOpenEditOrderDialog} className="flex-1 sm:flex-initial">
            <Edit className="mr-2 h-4 w-4" />
            <span className="sm:inline hidden">Chỉnh sửa</span>
            <span className="sm:hidden inline">Sửa</span>
          </Button>
        </div>
      </div>

      <Card className="overflow-hidden border-none shadow-md">
        <CardHeader className="border-b bg-muted/40 pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <CardTitle className="text-xl sm:text-2xl font-bold">
                {orderData ? `Đơn hàng #${orderData.id}` : 'Đang tải...'}
              </CardTitle>
              <CardDescription className="flex flex-col gap-1 mt-1 text-xs sm:text-sm">
                {orderData ?
                  <>
                    <span>Tạo lúc: {orderData.created_at ?
                      formatDateTimeVN(orderData.created_at) :
                      'Không có thông tin'}</span>
                    <span>Cập nhật: {orderData.updated_at ?
                      formatDateTimeVN(orderData.updated_at) :
                      'Không có thông tin'}</span>
                  </> :
                  'Đang tải...'}
              </CardDescription>
            </div>
            <Badge
              variant="outline"
              className={`${getStatusStyle(orderData?.status || 'pending').color} ${getStatusStyle(orderData?.status || 'pending').bgColor} ${getStatusStyle(orderData?.status || 'pending').borderColor} px-3 py-1.5 text-sm font-medium self-start sm:self-center mt-2 sm:mt-0`}
            >
              {statusStyles[orderData?.status as keyof typeof statusStyles]?.label || 'Chờ xử lý'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Order Details in Two Columns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-6">
            <div className="space-y-4 md:space-y-5">
              <h3 className="text-base md:text-lg font-semibold pb-2 border-b">Thông tin đơn hàng</h3>
              <div className="space-y-2 md:space-y-3">
                {/* Common fields for both order types */}
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Khách hàng:</span>
                  <span className="text-sm font-medium">{orderData?.customer?.name}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Loại giao dịch:</span>
                  {orderData?.order_type && (
                    <Badge
                      className={`
                        ${orderData.order_type === 'Đáo' ? 'bg-blue-500/10 text-blue-500 border-blue-500/20' :
                          orderData.order_type === 'Rút' ? 'bg-purple-500/10 text-purple-500 border-purple-500/20' :
                          'bg-green-500/10 text-green-500 border-green-500/20'}
                        flex items-center justify-center min-w-[60px] py-0.5 px-1.5 text-xs font-medium
                      `}
                    >
                      {orderData.order_type}
                    </Badge>
                  )}
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Số thẻ:</span>
                  <span className="text-sm font-medium">
                    {orderData?.card?.card_number} - {getBankName(orderData?.card?.bank_id)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Tổng số tiền:</span>
                  <span className="text-sm font-medium">{orderData?.total_amount ?
                    formatNumberWithDot(Number(orderData.total_amount)) :
                    'Không có thông tin'}</span>
                </div>

                {/* Fields specific to "Rút" order type */}
                {orderData?.order_type === 'Rút' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Phí (%):</span>
                      <span className="text-sm font-medium">{orderData?.fee_percentage ?
                        new Intl.NumberFormat('vi-VN', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }).format(Number(orderData.fee_percentage)) + '%' :
                        'Không có thông tin'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tiền phí:</span>
                      <span className="text-sm font-medium">{orderData?.charge_customer_fee ?
                        formatNumberWithDot(Number(orderData.charge_customer_fee)) :
                        'Không có thông tin'}</span>
                    </div>
                    {orderData?.order_type === 'Rút' && (
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Trừ nợ:</span>
                        <span className="text-sm font-medium">{orderData?.debt_deduction ?
                          formatNumberWithDot(Number(orderData.debt_deduction)) :
                          '0'}</span>
                      </div>
                    )}
                  </>
                )}

                {/* Fields specific to "Đáo" order type */}
                {orderData?.order_type === 'Đáo' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Phí (%):</span>
                      <span className="text-sm font-medium">{orderData?.fee_percentage ?
                        new Intl.NumberFormat('vi-VN', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        }).format(Number(orderData.fee_percentage)) + '%' :
                        'Không có thông tin'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tiền phí:</span>
                      <span className="text-sm font-medium">{orderData?.charge_customer_fee ?
                        formatNumberWithDot(Number(orderData.charge_customer_fee)) :
                        'Không có thông tin'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tiền âm:</span>
                      <span className="text-sm font-medium">{orderData?.negative_amount ?
                        formatNumberWithDot(Number(orderData.negative_amount)) :
                        '0'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tổng phí:</span>
                      <span className="text-sm font-medium">{orderData?.total_fee ?
                        formatNumberWithDot(Number(orderData.total_fee)) :
                        'Không có thông tin'}</span>
                    </div>
                  </>
                )}


                {/* Profit */}
                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">Lợi nhuận:</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-gray-100"
                      onClick={() => setShowProfitAdjustmentModal(true)}
                      title="Điều chỉnh lợi nhuận"
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                  <span className="text-sm font-medium">{orderData?.profit ?
                    formatNumberWithDot(Number(orderData.profit)) :
                    '0'}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4 md:space-y-5">
              <h3 className="text-base md:text-lg font-semibold pb-2 border-b">Tình trạng thanh toán</h3>
              <div className="space-y-3 md:space-y-4">
                {/* Payment status for "Quẹt thẻ" */}
                <div className="p-3 border rounded-md bg-muted/30">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-primary">Cần quẹt:</span>
                    <span className="text-sm font-semibold text-primary">
                      {formatNumberWithDot(Number(orderData?.need_to_swipe || 0))}
                    </span>
                  </div>

                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <span>Đã quẹt:</span>
                    <span>
                      {Number(orderData?.total_swiped || 0) > 0 ?
                        formatNumberWithDot(Number(orderData?.total_swiped || 0)) :
                        '0'}
                    </span>
                  </div>

                  <div className="mt-2 w-full bg-muted h-1.5 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary rounded-full"
                      style={{ width: formatPercent(calculateProgress(Number(orderData?.total_swiped || 0), Number(orderData?.total_amount || 0))) }}
                    ></div>
                  </div>
                </div>

                {/* Payment status for "Chuyển tiền" */}
                <div className="p-3 border rounded-md bg-muted/30">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-primary">Cần chuyển:</span>
                    <span className="text-sm font-semibold text-primary">
                      {formatNumberWithDot(Number(orderData?.need_to_transfer || 0))}
                    </span>
                  </div>

                  <div className="flex justify-between items-center text-xs text-muted-foreground">
                    <span>Đã chuyển:</span>
                    <span>
                      {Number(orderData?.total_transferred || 0) > 0 ?
                        formatNumberWithDot(Number(orderData?.total_transferred || 0)) :
                        '0'}
                    </span>
                  </div>

                  <div className="mt-2 w-full bg-muted h-1.5 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary rounded-full"
                      style={{ width: formatPercent(calculateProgress(Number(orderData?.total_transferred || 0), Number(orderData?.total_amount || 0))) }}
                    ></div>
                  </div>
                </div>


                                                              {/* Note */}
                                                              <div className="p-3 border rounded-md">
                  <span className="text-sm text-muted-foreground block mb-1">Ghi chú:</span>
                  <span className="text-sm block">{orderData?.note || 'Không có ghi chú'}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          <Tabs defaultValue="card-swipe" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4 h-auto">
              <TabsTrigger value="card-swipe" className="py-2 md:py-3 text-xs md:text-sm">Quẹt thẻ</TabsTrigger>
              <TabsTrigger value="money-transfer" className="py-2 md:py-3 text-xs md:text-sm">Chuyển tiền</TabsTrigger>
            </TabsList>

            <TabsContent value="card-swipe" className="mt-0">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                <h3 className="text-base md:text-lg font-medium">Chi tiết quẹt thẻ</h3>
                <Button onClick={handleOpenCardSwipeDialog} size="sm" className="w-full sm:w-auto">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Thêm lệnh quẹt
                </Button>
              </div>

              {/* Desktop Table - Hidden on mobile */}
              <div className="hidden md:block">
                <Card className="shadow-sm">
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader className="bg-muted/50">
                        <TableRow>
                          <TableHead>Mã QT</TableHead>
                          <TableHead>Thời gian</TableHead>
                          <TableHead>Số tiền</TableHead>
                          <TableHead>Máy POS</TableHead>
                          <TableHead>Phí bank</TableHead>
                          <TableHead>Tính tiền về</TableHead>
                          <TableHead>Ghi chú</TableHead>
                          <TableHead className="text-right">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {loadingCardSwipes ? (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center py-4">
                              <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                              <span className="mt-2 block text-sm text-muted-foreground">Đang tải dữ liệu...</span>
                            </TableCell>
                          </TableRow>
                        ) : cardSwipes.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={8} className="text-center py-4">
                              <span className="text-sm text-muted-foreground">Chưa có dữ liệu quẹt thẻ</span>
                            </TableCell>
                          </TableRow>
                        ) : (
                          cardSwipes.map((swipe, index) => (
                            <TableRow key={`${swipe.id}-${index}-${swipe.created_at}`}>
                              <TableCell>{swipe.id}</TableCell>
                              <TableCell>{formatDateTimeVN(swipe.created_at)}</TableCell>
                              <TableCell>{formatNumberWithDot(Number(swipe.amount))}</TableCell>
                              <TableCell>
                                {getPOSName(swipe.pos_id)}
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <span>{formatNumberWithDot(Number(swipe.bank_fee))}</span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 hover:bg-gray-100"
                                    onClick={() => {
                                      setSelectedCardSwipe(swipe);
                                      setShowBankFeeAdjustmentModal(true);
                                    }}
                                    title="Điều chỉnh phí bank"
                                  >
                                    <Settings className="h-3 w-3" />
                                  </Button>
                                </div>
                              </TableCell>
                              <TableCell>{formatNumberWithDot(Number(swipe.net_amount))}</TableCell>
                              <TableCell>{swipe.note || '-'}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    if (confirm('Bạn có chắc chắn muốn xóa quẹt thẻ này?')) {
                                      cardService.deleteCardSwipe(swipe.id)
                                        .then(async () => {
                                          toast.success('Đã xóa quẹt thẻ thành công');
                                          // Refresh card swipes
                                          const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
                                          setCardSwipes(swipesData || []);
                                          // Cập nhật trạng thái đơn hàng và tải lại dữ liệu
                                          await orderService.updateOrderStatus(Number(orderId));
                                          await fetchOrderData();
                                        })
                                        .catch(error => {
                                          console.error('Error deleting card swipe:', error);
                                          toast.error('Không thể xóa quẹt thẻ');
                                        });
                                    }
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>

              {/* Mobile Card View - Visible only on mobile */}
              <div className="md:hidden space-y-4">
                {loadingCardSwipes ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Đang tải dữ liệu...</span>
                  </div>
                ) : cardSwipes.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Chưa có dữ liệu quẹt thẻ
                  </div>
                ) : (
                  cardSwipes.map((swipe, index) => (
                    <Card key={`${swipe.id}-${index}-${swipe.created_at}`} className="shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <div className="font-medium">Mã QT: {swipe.id}</div>
                            <div className="text-xs text-muted-foreground">
                              {formatDateTimeVN(swipe.created_at)}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              if (confirm('Bạn có chắc chắn muốn xóa quẹt thẻ này?')) {
                                cardService.deleteCardSwipe(swipe.id)
                                  .then(async () => {
                                    toast.success('Đã xóa quẹt thẻ thành công');
                                    // Refresh card swipes
                                    const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
                                    setCardSwipes(swipesData || []);
                                    // Cập nhật trạng thái đơn hàng và tải lại dữ liệu
                                    await orderService.updateOrderStatus(Number(orderId));
                                    await fetchOrderData();
                                  })
                                  .catch(error => {
                                    console.error('Error deleting card swipe:', error);
                                    toast.error('Không thể xóa quẹt thẻ');
                                  });
                              }
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-3">
                          <div>
                            <div className="text-xs text-muted-foreground">Số tiền</div>
                            <div className="text-sm font-medium">{formatNumberWithDot(Number(swipe.amount))}</div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">Máy POS</div>
                            <div className="text-sm">{getPOSName(swipe.pos_id)}</div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-3">
                          <div>
                            <div className="text-xs text-muted-foreground">Phí bank</div>
                            <div className="flex items-center gap-1">
                              <span className="text-sm">{formatNumberWithDot(Number(swipe.bank_fee))}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-5 w-5 p-0 hover:bg-gray-100"
                                onClick={() => {
                                  setSelectedCardSwipe(swipe);
                                  setShowBankFeeAdjustmentModal(true);
                                }}
                                title="Điều chỉnh phí bank"
                              >
                                <Settings className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div>
                            <div className="text-xs text-muted-foreground">Tính tiền về</div>
                            <div className="text-sm font-medium">{formatNumberWithDot(Number(swipe.net_amount))}</div>
                          </div>
                        </div>

                        {swipe.note && (
                          <div className="mt-2">
                            <div className="text-xs text-muted-foreground">Ghi chú</div>
                            <div className="text-sm">{swipe.note}</div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="money-transfer" className="mt-0">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
                <h3 className="text-base md:text-lg font-medium">Chi tiết chuyển tiền</h3>
                <Button onClick={handleOpenMoneyTransferDialog} size="sm" className="w-full sm:w-auto">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Thêm chuyển tiền
                </Button>
              </div>

              {/* Desktop Table - Hidden on mobile */}
              <div className="hidden md:block">
                <Card className="shadow-sm">
                  <CardContent className="p-0">
                    <Table>
                      <TableHeader className="bg-muted/50">
                        <TableRow>
                          <TableHead>Mã CT</TableHead>
                          <TableHead>Thời gian</TableHead>
                          <TableHead>Số tiền</TableHead>
                          <TableHead>Ghi chú</TableHead>
                          <TableHead className="text-right">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {loadingMoneyTransfers ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                              <p className="text-sm text-muted-foreground mt-2">Đang tải dữ liệu...</p>
                            </TableCell>
                          </TableRow>
                        ) : moneyTransfers.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-4">
                              <p className="text-sm text-muted-foreground">Chưa có dữ liệu chuyển tiền</p>
                            </TableCell>
                          </TableRow>
                        ) : (
                          moneyTransfers.map((transfer) => (
                            <TableRow key={transfer.id}>
                              <TableCell>{transfer.id}</TableCell>
                              <TableCell>{formatDateTimeVN(transfer.transfer_date)}</TableCell>
                              <TableCell>{formatNumberWithDot(Number(transfer.amount))}</TableCell>
                              <TableCell>{transfer.note || 'Không có ghi chú'}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={async () => {
                                    // Xử lý xóa chuyển tiền
                                    if (window.confirm('Bạn có chắc chắn muốn xóa chuyển tiền này?')) {
                                      try {
                                        // Gọi API xóa chuyển tiền
                                        await moneyTransferService.deleteMoneyTransfer(transfer.id!);
                                        toast.success('Đã xóa chuyển tiền thành công');

                                        // Cập nhật lại dữ liệu money transfers
                                        await fetchMoneyTransfers();

                                        // Cập nhật trạng thái đơn hàng và tải lại dữ liệu
                                        await orderService.updateOrderStatus(Number(orderId));
                                        await fetchOrderData();

                                        // Gọi callback để thông báo dữ liệu đã thay đổi
                                        if (onDataChange) onDataChange();
                                      } catch (error) {
                                        console.error('Error deleting money transfer:', error);
                                        toast.error('Không thể xóa chuyển tiền');
                                      }
                                    }
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>

              {/* Mobile Card View - Visible only on mobile */}
              <div className="md:hidden space-y-4">
                {loadingMoneyTransfers ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Đang tải dữ liệu...</span>
                  </div>
                ) : moneyTransfers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Chưa có dữ liệu chuyển tiền
                  </div>
                ) : (
                  moneyTransfers.map((transfer) => (
                    <Card key={transfer.id} className="shadow-sm">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <div className="font-medium">Mã CT: {transfer.id}</div>
                            <div className="text-xs text-muted-foreground">
                              {formatDateTimeVN(transfer.transfer_date)}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={async () => {
                              // Xử lý xóa chuyển tiền
                              if (window.confirm('Bạn có chắc chắn muốn xóa chuyển tiền này?')) {
                                try {
                                  // Gọi API xóa chuyển tiền
                                  await moneyTransferService.deleteMoneyTransfer(transfer.id!);
                                  toast.success('Đã xóa chuyển tiền thành công');

                                  // Cập nhật lại dữ liệu money transfers
                                  await fetchMoneyTransfers();

                                  // Cập nhật trạng thái đơn hàng và tải lại dữ liệu
                                  await orderService.updateOrderStatus(Number(orderId));
                                  await fetchOrderData();
                                } catch (error) {
                                  console.error('Error deleting money transfer:', error);
                                  toast.error('Không thể xóa chuyển tiền');
                                }
                              }
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>

                        <div className="mb-3">
                          <div className="text-xs text-muted-foreground">Số tiền</div>
                          <div className="text-sm font-medium">{formatNumberWithDot(Number(transfer.amount))}</div>
                        </div>

                        {transfer.note && (
                          <div>
                            <div className="text-xs text-muted-foreground">Ghi chú</div>
                            <div className="text-sm">{transfer.note}</div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {showCardSwipeDialog && (
        <Dialog open={showCardSwipeDialog} onOpenChange={setShowCardSwipeDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Thêm quẹt thẻ mới</DialogTitle>
              <DialogDescription>
                Thêm thông tin quẹt thẻ cho đơn hàng {orderId}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cardSwipeId" className="text-right">
                  Mã QT
                </Label>
                <Input
                  id="cardSwipeId"
                  value={cardSwipeForm.id}
                  className="col-span-3"
                  disabled
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cardSwipeCardNumber" className="text-right">
                  Số thẻ
                </Label>
                <Input
                  id="cardSwipeCardNumber"
                  value={cardSwipeForm.cardNumber}
                  className="col-span-3"
                  disabled
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cardSwipeAmount" className="text-right">
                  Số tiền
                </Label>
                <Input
                  id="cardSwipeAmount"
                  value={cardSwipeForm.amount}
                  className="col-span-3"
                  onChange={(e) => handleCardSwipeFormChange('amount', e.target.value)}
                  placeholder="Nhập số tiền"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cardSwipePos" className="text-right">
                  Máy POS
                </Label>
                <Select
                  value={cardSwipeForm.pos}
                  onValueChange={(value) => handleCardSwipeFormChange('pos', value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Chọn máy POS" />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingPosList ? (
                      <div className="flex items-center justify-center p-2">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Đang tải...</span>
                      </div>
                    ) : posList.length === 0 ? (
                      <div className="p-2 text-center text-sm text-muted-foreground">
                        Không có dữ liệu máy POS
                      </div>
                    ) : (
                      posList
                        .filter(pos => pos.status !== 'locked') // Chỉ hiển thị POS có trạng thái khác 'locked'
                        .map((pos) => (
                          <SelectItem key={pos.id} value={pos.id.toString()}>
                            {pos.name}
                          </SelectItem>
                        ))
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="cardSwipeNote" className="text-right">
                  Ghi chú
                </Label>
                <Input
                  id="cardSwipeNote"
                  value={cardSwipeForm.note}
                  className="col-span-3"
                  onChange={(e) => handleCardSwipeFormChange('note', e.target.value)}
                  placeholder="Nhập ghi chú (nếu có)"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCardSwipeDialog(false)}>
                Hủy
              </Button>
              <Button onClick={handleSubmitCardSwipe}>Lưu</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {showMoneyTransferDialog && (
        <Dialog open={showMoneyTransferDialog} onOpenChange={setShowMoneyTransferDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Thêm chuyển tiền mới</DialogTitle>
              <DialogDescription>
                Thêm thông tin chuyển tiền cho đơn hàng {orderId}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="transferId" className="text-right">
                  Mã CT
                </Label>
                <Input
                  id="transferId"
                  value={moneyTransferForm.id}
                  className="col-span-3"
                  disabled
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="transferAmount" className="text-right">
                  Số tiền
                </Label>
                <Input
                  id="transferAmount"
                  value={moneyTransferForm.amount}
                  className="col-span-3"
                  onChange={(e) => handleMoneyTransferFormChange('amount', e.target.value)}
                  placeholder="Nhập số tiền"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="transferNote" className="text-right">
                  Nội dung
                </Label>
                <Input
                  id="transferNote"
                  value={moneyTransferForm.note}
                  className="col-span-3"
                  onChange={(e) => handleMoneyTransferFormChange('note', e.target.value)}
                  placeholder="Nhập nội dung chuyển tiền"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowMoneyTransferDialog(false)}>
                Hủy
              </Button>
              <Button onClick={handleSubmitMoneyTransfer}>Lưu</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Dialog chỉnh sửa đơn hàng */}
      {showEditOrderDialog && (
        <Dialog open={showEditOrderDialog} onOpenChange={setShowEditOrderDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Chỉnh sửa đơn hàng</DialogTitle>
              <DialogDescription>
                Chỉnh sửa thông tin đơn hàng {orderId}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="orderId" className="text-right">
                  Mã đơn hàng
                </Label>
                <Input
                  id="orderId"
                  value={orderEditForm.id}
                  className="col-span-3"
                  disabled
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="orderAmount" className="text-right">
                  Tổng số tiền
                </Label>
                <Input
                  id="orderAmount"
                  value={orderEditForm.amount}
                  className="col-span-3"
                  onChange={(e) => handleOrderEditFormChange('amount', e.target.value)}
                  placeholder="Nhập tổng số tiền"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="orderFee" className="text-right">
                  Phí %
                </Label>
                <Input
                  id="orderFee"
                  value={orderEditForm.fee}
                  className="col-span-3"
                  onChange={(e) => handleOrderEditFormChange('fee', e.target.value)}
                  placeholder="Nhập phí %"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="orderCustomerFee" className="text-right">
                  Tiền phí
                </Label>
                <Input
                  id="orderCustomerFee"
                  value={orderEditForm.customerFee}
                  className="col-span-3"
                  onChange={(e) => handleOrderEditFormChange('customerFee', e.target.value)}
                  placeholder="Nhập tiền phí"
                />
              </div>
              {orderData?.order_type === 'Đáo' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="orderNegativeAmount" className="text-right">
                    Tiền âm
                  </Label>
                  <Input
                    id="orderNegativeAmount"
                    value={orderEditForm.negativeAmount}
                    className="col-span-3"
                    onChange={(e) => handleOrderEditFormChange('negativeAmount', e.target.value)}
                    placeholder="Nhập tiền âm"
                  />
                </div>
              )}
              {orderData?.order_type === 'Rút' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="orderDebtDeduction" className="text-right">
                    Trừ nợ
                  </Label>
                  <Input
                    id="orderDebtDeduction"
                    value={orderEditForm.debtDeduction}
                    className="col-span-3"
                    onChange={(e) => handleOrderEditFormChange('debtDeduction', e.target.value)}
                    placeholder="Nhập số tiền trừ nợ"
                  />
                </div>
              )}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="orderNote" className="text-right">
                  Ghi chú
                </Label>
                <Input
                  id="orderNote"
                  value={orderEditForm.note}
                  className="col-span-3"
                  onChange={(e) => handleOrderEditFormChange('note', e.target.value)}
                  placeholder="Nhập ghi chú"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditOrderDialog(false)}>
                Hủy
              </Button>
              <Button onClick={handleSubmitOrderEdit} disabled={editLoading}>
                {editLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Lưu
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Modal điều chỉnh lợi nhuận */}
      <ProfitAdjustmentModal
        isOpen={showProfitAdjustmentModal}
        onClose={() => setShowProfitAdjustmentModal(false)}
        orderId={Number(orderId)}
        currentProfit={orderData?.profit ? Number(orderData.profit) : 0}
        onSuccess={async (updatedOrder) => {
          // Nếu có dữ liệu order được trả về, cập nhật ngay lập tức
          if (updatedOrder) {
            setOrderData(updatedOrder);
          } else {
            // Nếu không có, tải lại dữ liệu đơn hàng
            await fetchOrderData();
          }
          if (onDataChange) onDataChange();
        }}
      />

      {/* Modal điều chỉnh phí bank */}
      <BankFeeAdjustmentModal
        isOpen={showBankFeeAdjustmentModal}
        onClose={() => {
          setShowBankFeeAdjustmentModal(false);
          setSelectedCardSwipe(null);
        }}
        cardSwipe={selectedCardSwipe}
        onSuccess={async () => {
          // Tải lại dữ liệu card swipes và đơn hàng sau khi điều chỉnh thành công
          const swipesData = await cardService.getCardSwipesByOrderId(Number(orderId));
          setCardSwipes(swipesData || []);
          await fetchOrderData();
          if (onDataChange) onDataChange();
        }}
      />

      {/* Không cần OrderCreateModal ở đây nữa vì đã được bao gồm trong CreateOrderButton */}
    </div>
  );
}
