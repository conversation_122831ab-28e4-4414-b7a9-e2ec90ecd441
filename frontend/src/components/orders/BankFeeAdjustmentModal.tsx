import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, CreditCard, TrendingUp, TrendingDown } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface CardSwipe {
  id: number;
  amount: number;
  bank_fee: number;
  net_amount: number;
  order_id: number;
}

interface BankFeeAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  cardSwipe: CardSwipe | null;
  onSuccess: () => void;
}

export function BankFeeAdjustmentModal({
  isOpen,
  onClose,
  cardSwipe,
  onSuccess
}: BankFeeAdjustmentModalProps) {
  const [loading, setLoading] = useState(false);
  const [newBankFee, setNewBankFee] = useState('');
  const [adjustmentReason, setAdjustmentReason] = useState('');

  // Reset form khi modal mở
  useEffect(() => {
    if (isOpen) {
      setNewBankFee('');
      setAdjustmentReason('');
    }
  }, [isOpen]);

  if (!cardSwipe) return null;

  // Tính toán các giá trị
  const currentBankFee = cardSwipe.bank_fee;
  const swipeAmount = cardSwipe.amount;
  const currentNetAmount = cardSwipe.net_amount;
  
  const numericNewBankFee = newBankFee ? parseFloat(newBankFee.replace(/\./g, '')) : 0;
  const newNetAmount = swipeAmount - numericNewBankFee;
  const bankFeeDifference = numericNewBankFee - currentBankFee;
  const netAmountDifference = newNetAmount - currentNetAmount;

  // Format số với dấu chấm phân cách hàng nghìn
  const formatNumberWithDot = (value: string) => {
    const numericValue = value.replace(/\D/g, '');
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Xử lý thay đổi input phí bank
  const handleBankFeeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatNumberWithDot(e.target.value);
    setNewBankFee(formatted);
  };

  // Xử lý submit
  const handleSubmit = async () => {
    if (!newBankFee.trim()) {
      toast.error('Vui lòng nhập phí bank mới');
      return;
    }

    if (!adjustmentReason.trim()) {
      toast.error('Vui lòng nhập lý do điều chỉnh');
      return;
    }

    if (isNaN(numericNewBankFee) || numericNewBankFee < 0) {
      toast.error('Phí bank không hợp lệ');
      return;
    }

    if (numericNewBankFee > swipeAmount) {
      toast.error('Phí bank không thể lớn hơn số tiền quẹt');
      return;
    }

    try {
      setLoading(true);
      
      const response = await apiClient.put(`/api/card-swipes/${cardSwipe.id}/adjust-bank-fee`, {
        new_bank_fee: numericNewBankFee,
        adjustment_reason: adjustmentReason.trim()
      });

      if (response.data.success) {
        toast.success('Điều chỉnh phí bank thành công');
        onSuccess();
        onClose();
      } else {
        toast.error(response.data.message || 'Có lỗi xảy ra');
      }
    } catch (error: any) {
      console.error('Error adjusting bank fee:', error);
      const errorMessage = error.response?.data?.message || 'Không thể điều chỉnh phí bank';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Điều chỉnh phí bank
          </DialogTitle>
          <DialogDescription>
            Điều chỉnh phí bank cho giao dịch quẹt thẻ #{cardSwipe.id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Thông tin giao dịch hiện tại */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <Label className="text-sm font-medium text-gray-600">
                Số tiền quẹt
              </Label>
              <div className="text-lg font-semibold text-gray-900">
                {swipeAmount.toLocaleString('vi-VN')}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">
                Phí bank hiện tại
              </Label>
              <div className="text-lg font-semibold text-red-600">
                {currentBankFee.toLocaleString('vi-VN')}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">
                Tiền về hiện tại
              </Label>
              <div className="text-lg font-semibold text-green-600">
                {currentNetAmount.toLocaleString('vi-VN')}
              </div>
            </div>
          </div>

          {/* Phí bank mới */}
          <div className="space-y-2">
            <Label htmlFor="newBankFee">Phí bank mới *</Label>
            <Input
              id="newBankFee"
              type="text"
              value={newBankFee}
              onChange={handleBankFeeChange}
              placeholder="Nhập phí bank mới"
              className="text-right"
            />
          </div>

          {/* Hiển thị tính toán mới */}
          {newBankFee && (
            <div className="grid grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
              <div>
                <Label className="text-sm font-medium text-blue-700">
                  Tiền về mới
                </Label>
                <div className="text-lg font-semibold text-green-600">
                  {newNetAmount.toLocaleString('vi-VN')}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-blue-700">
                  Chênh lệch tiền về
                </Label>
                <div className={`flex items-center gap-1 text-lg font-semibold ${
                  netAmountDifference > 0 ? 'text-green-600' :
                  netAmountDifference < 0 ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {netAmountDifference > 0 && <TrendingUp className="h-4 w-4" />}
                  {netAmountDifference < 0 && <TrendingDown className="h-4 w-4" />}
                  {netAmountDifference > 0 ? '+' : ''}{netAmountDifference.toLocaleString('vi-VN')}
                </div>
              </div>
            </div>
          )}

          {/* Hiển thị chênh lệch phí */}
          {newBankFee && (
            <div className="p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-yellow-700">
                  Chênh lệch phí bank:
                </span>
                <div className={`flex items-center gap-1 font-semibold ${
                  bankFeeDifference > 0 ? 'text-red-600' : 
                  bankFeeDifference < 0 ? 'text-green-600' : 'text-gray-600'
                }`}>
                  {bankFeeDifference > 0 && <TrendingUp className="h-4 w-4" />}
                  {bankFeeDifference < 0 && <TrendingDown className="h-4 w-4" />}
                  {bankFeeDifference > 0 ? '+' : ''}{bankFeeDifference.toLocaleString('vi-VN')}
                </div>
              </div>
            </div>
          )}

          {/* Lý do điều chỉnh */}
          <div className="space-y-2">
            <Label htmlFor="adjustmentReason">Lý do điều chỉnh *</Label>
            <Textarea
              id="adjustmentReason"
              value={adjustmentReason}
              onChange={(e) => setAdjustmentReason(e.target.value)}
              placeholder="Nhập lý do điều chỉnh phí bank..."
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xác nhận điều chỉnh
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
