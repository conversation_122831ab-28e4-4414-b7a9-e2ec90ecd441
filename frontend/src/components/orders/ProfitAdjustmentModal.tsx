import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, TrendingUp, TrendingDown } from "lucide-react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";

interface ProfitAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: number;
  currentProfit: number;
  onSuccess: (updatedOrder?: any) => void;
}

export function ProfitAdjustmentModal({
  isOpen,
  onClose,
  orderId,
  currentProfit,
  onSuccess
}: ProfitAdjustmentModalProps) {
  const [loading, setLoading] = useState(false);
  const [newProfit, setNewProfit] = useState('');
  const [adjustmentReason, setAdjustmentReason] = useState('');

  // Reset form khi modal mở
  useEffect(() => {
    if (isOpen) {
      setNewProfit('');
      setAdjustmentReason('');
    }
  }, [isOpen]);

  // Tính toán chênh lệch
  const profitDifference = newProfit ? parseFloat(newProfit.replace(/\./g, '').replace(/^-/, '-')) - currentProfit : 0;

  // Format số với dấu chấm phân cách hàng nghìn (cho phép số âm)
  const formatNumberWithDot = (value: string) => {
    // Giữ lại dấu trừ nếu có ở đầu
    const isNegative = value.startsWith('-');
    // Loại bỏ tất cả ký tự không phải số
    const numericValue = value.replace(/[^0-9]/g, '');
    // Thêm dấu chấm phân cách hàng nghìn
    const formatted = numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    // Trả về với dấu trừ nếu cần
    return isNegative ? '-' + formatted : formatted;
  };

  // Xử lý thay đổi input lợi nhuận
  const handleProfitChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatNumberWithDot(e.target.value);
    setNewProfit(formatted);
  };

  // Xử lý submit
  const handleSubmit = async () => {
    if (!newProfit.trim()) {
      toast.error('Vui lòng nhập lợi nhuận mới');
      return;
    }

    if (!adjustmentReason.trim()) {
      toast.error('Vui lòng nhập lý do điều chỉnh');
      return;
    }

    const numericProfit = parseFloat(newProfit.replace(/\./g, '').replace(/^-/, '-'));
    if (isNaN(numericProfit)) {
      toast.error('Lợi nhuận không hợp lệ');
      return;
    }

    try {
      setLoading(true);
      
      const response = await apiClient.put(`/api/orders/${orderId}/adjust-profit`, {
        new_profit: numericProfit,
        adjustment_reason: adjustmentReason.trim()
      });

      if (response.data.success) {
        toast.success('Điều chỉnh lợi nhuận thành công');
        // Gọi callback với dữ liệu order đã cập nhật nếu có
        onSuccess(response.data.data?.order);
        onClose();
      } else {
        toast.error(response.data.message || 'Có lỗi xảy ra');
      }
    } catch (error: any) {
      console.error('Error adjusting profit:', error);
      const errorMessage = error.response?.data?.message || 'Không thể điều chỉnh lợi nhuận';
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Điều chỉnh lợi nhuận đơn hàng
          </DialogTitle>
          <DialogDescription>
            Điều chỉnh lợi nhuận của đơn hàng #{orderId}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Lợi nhuận hiện tại */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium text-gray-600">
              Lợi nhuận hiện tại
            </Label>
            <div className="text-lg font-semibold text-gray-900">
              {currentProfit.toLocaleString('vi-VN')}
            </div>
          </div>

          {/* Lợi nhuận mới */}
          <div className="space-y-2">
            <Label htmlFor="newProfit">Lợi nhuận mới *</Label>
            <Input
              id="newProfit"
              type="text"
              value={newProfit}
              onChange={handleProfitChange}
              placeholder="Nhập lợi nhuận mới"
              className="text-right"
            />
          </div>

          {/* Hiển thị chênh lệch */}
          {newProfit && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-700">
                  Chênh lệch:
                </span>
                <div className={`flex items-center gap-1 font-semibold ${
                  profitDifference > 0 ? 'text-green-600' : 
                  profitDifference < 0 ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {profitDifference > 0 && <TrendingUp className="h-4 w-4" />}
                  {profitDifference < 0 && <TrendingDown className="h-4 w-4" />}
                  {profitDifference > 0 ? '+' : ''}{profitDifference.toLocaleString('vi-VN')}
                </div>
              </div>
            </div>
          )}

          {/* Lý do điều chỉnh */}
          <div className="space-y-2">
            <Label htmlFor="adjustmentReason">Lý do điều chỉnh *</Label>
            <Textarea
              id="adjustmentReason"
              value={adjustmentReason}
              onChange={(e) => setAdjustmentReason(e.target.value)}
              placeholder="Nhập lý do điều chỉnh lợi nhuận..."
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Xác nhận điều chỉnh
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
