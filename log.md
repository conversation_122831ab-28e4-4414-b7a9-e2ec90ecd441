Order ID: 718
Request body: { new_profit: 150000, adjustment_reason: 'qbc' }
Found order: { id: 718, current_profit: 141500 }
Profit values: { old_profit: 141500, new_profit: 150000, profit_difference: 8500 }
Updating order with: {
  profit: 150000,
  note: '[<PERSON>i<PERSON>u chỉnh lợi nhuận] tính nhầm - Từ 141.500₫ thành -200.000₫\n' +
    '[<PERSON><PERSON><PERSON><PERSON> chỉnh lợi nhuận] lỗi - Từ 141.500 thành -200.000\n' +
    '[<PERSON><PERSON><PERSON>u chỉnh lợi nhuận] đơn lỗ - Từ 141.500 thành -300.000\n' +
    '[<PERSON><PERSON><PERSON>u chỉnh lợi nhuận] abc - Từ 141.500 thành -300.000\n' +
    '[<PERSON><PERSON>ều chỉnh lợi nhuận] lãi - Từ 141.500 thành 150.000\n' +
    '[<PERSON><PERSON><PERSON><PERSON> chỉnh lợ<PERSON> nhuận] abc - Từ 141.500 thành 150.000\n' +
    '[<PERSON><PERSON><PERSON>u chỉnh lợi nhuận] qbc - Từ 141.500 thành 150.000'
}
Date 2025-06-20 added to recalculation queue. Queue size: 1
Processing 2025-06-20 from recalculation queue. Remaining: 0
Recalculating financial report for date: 2025-06-20
Dashboard cache invalidated
Transaction cache invalidated
Report cache invalidated
KPI cache invalidated
Cache invalidated due to Order change
Report cache invalidated
Dashboard cache invalidated
KPI cache invalidated
Important cache invalidated
Date 2025-06-20 added to recalculation queue. Queue size: 1
Order updated successfully
Order profit after reload: 141500
Profit not updated correctly, using raw query
Order profit after raw query: 141500
Financial report for 2025-06-20 updated.
Cache cleared for specific date: report:date:2025-06-20
Cache cleared for report ranges potentially including 2025-06-20
Cache cleared for monthly report: report:monthly:2025-06
Cache cleared for report date: 2025-06-20
Updated order from DB: {
  id: 718,
  profit: 141500,
  note: '[Điều chỉnh lợi nhuận] tính nhầm - Từ 141.500₫ thành -200.000₫\n' +
    '[Điều chỉnh lợi nhuận] lỗi - Từ 141.500 thành -200.000\n' +
    '[Điều chỉnh lợi nhuận] đơn lỗ - Từ 141.500 thành -300.000\n' +
    '[Điều chỉnh lợi nhuận] abc - Từ 141.500 thành -300.000\n' +
    '[Điều chỉnh lợi nhuận] lãi - Từ 141.500 thành 150.000\n' +
    '[Điều chỉnh lợi nhuận] abc - Từ 141.500 thành 150.000\n' +
    '[Điều chỉnh lợi nhuận] qbc - Từ 141.500 thành 150.000'
}
Formatted order profit: 150000
Response data: {
  "success": true,
  "message": "Điều chỉnh lợi nhuận thành công",
  "data": {
    "adjustment_info": {
      "id": 718,
      "old_profit": 141500,
      "new_profit": 150000,
      "profit_difference": 8500,
      "adjustment_reason": "qbc",
      "updated_at": "2025-06-21T03:58:13.000Z"
    },
    "order": {
      "id": 718,
      "card_id": 518,
      "order_date": "2025-06-20T07:53:53.000Z",
      "total_amount": 50000000,
      "fee_percentage": 1.35,
      "total_swiped": 50000000,
      "need_to_swipe": 0,
      "total_transferred": 50000000,
      "need_to_transfer": 0,
      "note": "[Điều chỉnh lợi nhuận] tính nhầm - Từ 141.500₫ thành -200.000₫\n[Điều chỉnh lợi nhuận] lỗi - Từ 141.500 thành -200.000\n[Điều chỉnh lợi nhuận] đơn lỗ - Từ 141.500 thành -300.000\n[Điều chỉnh lợi nhuận] abc - Từ 141.500 thành -300.000\n[Điều chỉnh lợi nhuận] lãi - Từ 141.500 thành 150.000\n[Điều chỉnh lợi nhuận] abc - Từ 141.500 thành 150.000\n[Điều chỉnh lợi nhuận] qbc - Từ 141.500 thành 150.000",
      "status": "completed",
      "order_type": "Đáo",
      "charge_customer_fee": 675000,
      "negative_amount": 0,
      "debt_deduction": 0,
      "total_fee": 675000,
      "profit": 150000,
      "created_at": "2025-06-20T07:53:53.000Z",
      "updated_at": "2025-06-21T03:58:13.000Z",
      "customer": {
        "id": 79,
        "name": "trung nghiêm xá",
        "phone": "00000"
      },
      "card": {
        "id": 518,
        "card_number": "5239-5107-nguyen thi ha",
        "bank_id": 7
      }
    }
  }
}
=== END ADJUST PROFIT DEBUG ===

CREATE TRIGGER `after_card_swipe_delete` AFTER DELETE ON `card_swipes`
 FOR EACH ROW BEGIN
  -- Lấy thông tin order_type
  DECLARE v_order_type VARCHAR(10);
  DECLARE v_negative_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);

  SELECT order_type, IFNULL(negative_amount, 0), IFNULL(charge_customer_fee, 0)
  INTO v_order_type, v_negative_amount, v_charge_customer_fee
  FROM orders
  WHERE id = OLD.order_id;

  -- Cập nhật orders dựa vào order_type
  IF v_order_type = 'Đáo' THEN
    UPDATE orders
    SET total_swiped = total_swiped - OLD.amount,
        need_to_swipe = total_amount - ((total_swiped - OLD.amount) + negative_amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = OLD.order_id)
    WHERE id = OLD.order_id;
  ELSE
    UPDATE orders
    SET total_swiped = total_swiped - OLD.amount,
        need_to_swipe = total_amount - (total_swiped - OLD.amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = OLD.order_id)
    WHERE id = OLD.order_id;
  END IF;

  -- Cập nhật balance của POS
  UPDATE pos_terminals
  SET balance = balance - OLD.net_amount
  WHERE id = OLD.pos_id;
END

CREATE TRIGGER `after_card_swipe_insert` AFTER INSERT ON `card_swipes`
 FOR EACH ROW BEGIN
  -- Lấy thông tin order_type
  DECLARE v_order_type VARCHAR(10);
  DECLARE v_negative_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);

  SELECT order_type, IFNULL(negative_amount, 0), IFNULL(charge_customer_fee, 0)
  INTO v_order_type, v_negative_amount, v_charge_customer_fee
  FROM orders
  WHERE id = NEW.order_id;

  -- Cập nhật orders dựa vào order_type
  IF v_order_type = 'Đáo' THEN
    UPDATE orders
    SET total_swiped = total_swiped + NEW.amount,
        need_to_swipe = total_amount - ((total_swiped + NEW.amount) + negative_amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = NEW.order_id)
    WHERE id = NEW.order_id;
  ELSE
    UPDATE orders
    SET total_swiped = total_swiped + NEW.amount,
        need_to_swipe = total_amount - (total_swiped + NEW.amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = NEW.order_id)
    WHERE id = NEW.order_id;
  END IF;

  -- Cập nhật balance của POS
  UPDATE pos_terminals
  SET balance = balance + NEW.net_amount
  WHERE id = NEW.pos_id;
END

CREATE TRIGGER `after_card_swipe_update_manual_adjustment` AFTER UPDATE ON `card_swipes`
 FOR EACH ROW BEGIN
  -- Khai báo biến
  DECLARE v_order_type VARCHAR(10);
  DECLARE v_negative_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_total_bank_fee DECIMAL(15,2);

  -- Lấy thông tin đơn hàng
  SELECT order_type, IFNULL(negative_amount, 0), IFNULL(charge_customer_fee, 0)
  INTO v_order_type, v_negative_amount, v_charge_customer_fee
  FROM orders
  WHERE id = NEW.order_id;

  -- Tính tổng bank_fee từ tất cả card_swipes của đơn hàng
  SELECT COALESCE(SUM(bank_fee), 0) INTO v_total_bank_fee
  FROM card_swipes
  WHERE order_id = NEW.order_id;

  -- Cập nhật profit của đơn hàng
  -- Profit = charge_customer_fee - tổng bank_fee
  UPDATE orders
  SET profit = v_charge_customer_fee - v_total_bank_fee,
      updated_at = NOW()
  WHERE id = NEW.order_id;

  -- Cập nhật balance của POS nếu net_amount thay đổi
  IF OLD.net_amount != NEW.net_amount THEN
    -- Trừ số tiền cũ và cộng số tiền mới
    UPDATE pos_terminals
    SET balance = balance - OLD.net_amount + NEW.net_amount
    WHERE id = NEW.pos_id;
  END IF;

  -- Nếu POS thay đổi, cập nhật balance cho cả POS cũ và mới
  IF OLD.pos_id != NEW.pos_id THEN
    -- Trừ từ POS cũ
    UPDATE pos_terminals
    SET balance = balance - OLD.net_amount
    WHERE id = OLD.pos_id;
    
    -- Cộng vào POS mới
    UPDATE pos_terminals
    SET balance = balance + NEW.net_amount
    WHERE id = NEW.pos_id;
  END IF;
END

CREATE TRIGGER `after_money_return_delete` AFTER DELETE ON `money_returns`
 FOR EACH ROW BEGIN
  -- Cập nhật balance của POS
  IF OLD.pos_id IS NOT NULL THEN
    UPDATE pos_terminals
    SET balance = balance + OLD.amount
    WHERE id = OLD.pos_id;
  END IF;

  -- Cập nhật outstanding_fee của customer
  IF OLD.customer_id IS NOT NULL THEN
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount
    WHERE id = OLD.customer_id;
  END IF;
END

CREATE TRIGGER `after_money_return_insert` AFTER INSERT ON `money_returns`
 FOR EACH ROW BEGIN
  -- Cập nhật balance của POS
  IF NEW.pos_id IS NOT NULL THEN
    UPDATE pos_terminals
    SET balance = balance - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  -- Cập nhật outstanding_fee của customer
  IF NEW.customer_id IS NOT NULL THEN
    UPDATE customers
    SET outstanding_fee = outstanding_fee - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;
END

CREATE TRIGGER `after_money_return_update` AFTER UPDATE ON `money_returns`
 FOR EACH ROW BEGIN
  -- Xử lý khi thay đổi pos_id
  IF OLD.pos_id IS NOT NULL AND (NEW.pos_id IS NULL OR NEW.pos_id != OLD.pos_id) THEN
    -- Hoàn lại balance cho POS cũ
    UPDATE pos_terminals
    SET balance = balance + OLD.amount
    WHERE id = OLD.pos_id;
  END IF;

  IF NEW.pos_id IS NOT NULL AND (OLD.pos_id IS NULL OR NEW.pos_id != OLD.pos_id) THEN
    -- Cập nhật balance cho POS mới
    UPDATE pos_terminals
    SET balance = balance - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  -- Xử lý khi thay đổi customer_id
  IF OLD.customer_id IS NOT NULL AND (NEW.customer_id IS NULL OR NEW.customer_id != OLD.customer_id) THEN
    -- Hoàn lại outstanding_fee cho customer cũ
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount
    WHERE id = OLD.customer_id;
  END IF;

  IF NEW.customer_id IS NOT NULL AND (OLD.customer_id IS NULL OR NEW.customer_id != OLD.customer_id) THEN
    -- Cập nhật outstanding_fee cho customer mới
    UPDATE customers
    SET outstanding_fee = outstanding_fee - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;

  -- Xử lý khi chỉ thay đổi amount
  IF OLD.pos_id IS NOT NULL AND NEW.pos_id = OLD.pos_id AND OLD.amount != NEW.amount THEN
    -- Cập nhật balance cho POS
    UPDATE pos_terminals
    SET balance = balance + OLD.amount - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  IF OLD.customer_id IS NOT NULL AND NEW.customer_id = OLD.customer_id AND OLD.amount != NEW.amount THEN
    -- Cập nhật outstanding_fee cho customer
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;
END

CREATE TRIGGER `after_money_transfer_delete` AFTER DELETE ON `money_transfers`
 FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = OLD.order_id;

  UPDATE orders
  SET total_transferred = total_transferred - OLD.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred - OLD.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred - OLD.amount)
        ELSE 0
      END
  WHERE id = OLD.order_id;
END

CREATE TRIGGER `after_money_transfer_insert` AFTER INSERT ON `money_transfers`
 FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  -- Lấy thông tin đơn hàng
  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = NEW.order_id;

  -- Cập nhật đơn hàng
  UPDATE orders
  SET total_transferred = total_transferred + NEW.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred + NEW.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred + NEW.amount)
        ELSE 0
      END
  WHERE id = NEW.order_id;
END

CREATE TRIGGER `after_money_transfer_update` AFTER UPDATE ON `money_transfers`
 FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  -- Lấy thông tin đơn hàng
  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = NEW.order_id;

  -- Cập nhật đơn hàng
  UPDATE orders
  SET total_transferred = total_transferred - OLD.amount + NEW.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred - OLD.amount + NEW.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred - OLD.amount + NEW.amount)
        ELSE 0
      END
  WHERE id = NEW.order_id;
END

CREATE TRIGGER `after_order_status_update` AFTER UPDATE ON `orders`
 FOR EACH ROW BEGIN
  -- Khai báo biến ở đầu khối BEGIN
  DECLARE v_customer_id INT;
  DECLARE v_customer_name VARCHAR(100);
  DECLARE v_money_return_exists INT DEFAULT 0;

  -- Tạo money_returns cho đơn hàng "Rút" có giá trị "Trừ Nợ" khi chuyển sang trạng thái "completed"
  IF NEW.status = 'completed' AND OLD.status != 'completed' AND NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 THEN
    -- Kiểm tra xem đã có bản ghi money_returns cho đơn hàng này chưa
    SELECT COUNT(*) INTO v_money_return_exists
    FROM money_returns
    WHERE order_id = NEW.id;
    
    -- Chỉ tạo bản ghi nếu chưa tồn tại
    IF v_money_return_exists = 0 THEN
      -- Lấy customer_id từ card_id
      SELECT customer_id INTO v_customer_id
      FROM cards
      WHERE id = NEW.card_id;
      
      -- Tạo bản ghi money_returns
      INSERT INTO money_returns (customer_id, amount, note, user_id, order_id, created_at, updated_at)
      VALUES (v_customer_id, NEW.debt_deduction, CONCAT('Trừ nợ từ đơn hàng Rút #', NEW.id), NEW.user_id, NEW.id, NOW(), NOW());
    END IF;
  END IF;
  
  -- Xử lý khi trạng thái đơn hàng thay đổi từ completed sang khác
  IF OLD.status = 'completed' AND NEW.status != 'completed' AND OLD.order_type = 'Rút' AND IFNULL(OLD.debt_deduction, 0) > 0 THEN
    -- Xóa bản ghi money_returns liên quan
    DELETE FROM money_returns
    WHERE order_id = NEW.id;
  END IF;
END

CREATE TRIGGER `before_money_return_insert` BEFORE INSERT ON `money_returns`
 FOR EACH ROW BEGIN
  -- Đảm bảo khi customer_id có giá trị, pos_id phải là NULL
  IF NEW.customer_id IS NOT NULL AND NEW.pos_id IS NOT NULL THEN
    SIGNAL SQLSTATE '45000'
    SET MESSAGE_TEXT = 'Không thể có cả pos_id và customer_id cùng lúc';
  END IF;
END

CREATE TRIGGER `before_money_return_update` BEFORE UPDATE ON `money_returns`
 FOR EACH ROW BEGIN
  -- Đảm bảo khi customer_id có giá trị, pos_id phải là NULL
  IF NEW.customer_id IS NOT NULL AND NEW.pos_id IS NOT NULL THEN
    SIGNAL SQLSTATE '45000'
    SET MESSAGE_TEXT = 'Không thể có cả pos_id và customer_id cùng lúc';
  END IF;
END

CREATE TRIGGER `before_order_delete` BEFORE DELETE ON `orders`
 FOR EACH ROW BEGIN
  DECLARE v_customer_id INT;
  
  -- Lấy customer_id từ card_id của đơn hàng
  SELECT customer_id INTO v_customer_id
  FROM cards
  WHERE id = OLD.card_id;
  
  -- Xử lý dựa vào loại đơn hàng
  IF OLD.order_type = 'Đáo' THEN
    -- Hoàn lại outstanding_fee cho customer
    IF OLD.card_id IS NOT NULL AND OLD.total_fee > 0 THEN
      UPDATE customers
      SET outstanding_fee = outstanding_fee - OLD.total_fee
      WHERE id = v_customer_id;
    END IF;
    
    -- Cập nhật balance của POS cho đơn hàng Đáo
    UPDATE pos_terminals p
    JOIN (
      SELECT pos_id, SUM(net_amount) as total_net_amount
      FROM card_swipes
      WHERE order_id = OLD.id
      GROUP BY pos_id
    ) cs ON p.id = cs.pos_id
    SET p.balance = p.balance - cs.total_net_amount;
  ELSEIF OLD.order_type = 'Rút' THEN
    -- Xóa các bản ghi money_returns liên quan đến đơn hàng "Rút" có Trừ Nợ
    IF IFNULL(OLD.debt_deduction, 0) > 0 THEN
      DELETE FROM money_returns
      WHERE order_id = OLD.id;
    END IF;
    
    -- Cập nhật balance của POS cho đơn hàng Rút
    UPDATE pos_terminals p
    JOIN (
      SELECT pos_id, SUM(net_amount) as total_net_amount
      FROM card_swipes
      WHERE order_id = OLD.id
      GROUP BY pos_id
    ) cs ON p.id = cs.pos_id
    SET p.balance = p.balance - cs.total_net_amount;
  END IF;
  
  -- Lưu ý:
  -- 1. card_swipes và money_transfers sẽ tự động bị xóa do ON DELETE CASCADE
  -- 2. POS.balance đã được cập nhật trực tiếp trong trigger này
END

CREATE TRIGGER `before_order_insert` BEFORE INSERT ON `orders`
 FOR EACH ROW BEGIN
  -- Khai báo biến ở đầu khối BEGIN
  DECLARE v_outstanding_fee DECIMAL(15,2);
  DECLARE v_customer_id INT;
  
  -- Tính toán need_to_swipe dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_swipe = NEW.total_amount - (NEW.total_swiped + IFNULL(NEW.negative_amount, 0));
  ELSE
    SET NEW.need_to_swipe = NEW.total_amount - NEW.total_swiped;
  END IF;

  -- Tính toán need_to_transfer dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_transfer = NEW.total_amount - NEW.total_transferred;
  ELSEIF NEW.order_type = 'Rút' THEN
    SET NEW.need_to_transfer = NEW.total_amount - IFNULL(NEW.charge_customer_fee, 0) - IFNULL(NEW.debt_deduction, 0) - NEW.total_transferred;
    -- Đảm bảo đơn hàng "Rút" không có negative_amount
    SET NEW.negative_amount = 0;
  END IF;

  -- Tính toán total_fee cho tất cả loại đơn hàng
  SET NEW.total_fee = IFNULL(NEW.charge_customer_fee, 0) + IFNULL(NEW.negative_amount, 0);

  -- Tính toán profit ban đầu
  -- Lưu ý: profit sẽ được cập nhật lại trong trigger after_card_swipe_insert/update
  SET NEW.profit = IFNULL(NEW.charge_customer_fee, 0);

  -- Cập nhật outstanding_fee của customer thông qua card_id
  -- Chỉ áp dụng cho đơn hàng "Đáo", không áp dụng cho đơn hàng "Rút"
  IF NEW.card_id IS NOT NULL AND NEW.order_type = 'Đáo' AND NEW.total_fee > 0 THEN
    UPDATE customers c
    JOIN cards cd ON c.id = cd.customer_id
    SET c.outstanding_fee = c.outstanding_fee + NEW.total_fee
    WHERE cd.id = NEW.card_id;
  END IF;
  
  -- Kiểm tra outstanding_fee > 0 khi tạo đơn hàng "Rút" có giá trị "Trừ Nợ"
  IF NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 THEN
    -- Lấy customer_id từ card_id
    SELECT customer_id INTO v_customer_id
    FROM cards
    WHERE id = NEW.card_id;
    
    -- Lấy outstanding_fee của customer
    SELECT outstanding_fee INTO v_outstanding_fee
    FROM customers
    WHERE id = v_customer_id;
    
    -- Kiểm tra outstanding_fee > 0
    IF v_outstanding_fee <= 0 THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Khách hàng không có nợ phí, không thể áp dụng Trừ Nợ';
    END IF;
    
    -- Kiểm tra debt_deduction <= (total_swiped - charge_customer_fee)
    -- Lưu ý: Đối với đơn hàng mới, total_swiped thường là 0, nên điều kiện này sẽ ngăn người dùng nhập giá trị Trừ Nợ > 0
    -- Người dùng cần quẹt thẻ trước khi có thể áp dụng Trừ Nợ
    IF NEW.debt_deduction > (NEW.total_swiped - IFNULL(NEW.charge_customer_fee, 0)) THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Số tiền Trừ Nợ không được vượt quá (Tổng đã quẹt - Tiền phí)';
    END IF;
  END IF;
END

CREATE TRIGGER `before_order_update` BEFORE UPDATE ON `orders`
 FOR EACH ROW BEGIN
  -- Khai báo tất cả biến ở đầu khối BEGIN
  DECLARE v_outstanding_fee DECIMAL(15,2);
  DECLARE v_customer_id INT;
  DECLARE v_bank_fee_sum DECIMAL(15,2);
  DECLARE v_money_return_id INT;
  
  -- Tính tổng bank_fee từ card_swipes
  SELECT COALESCE(SUM(bank_fee), 0) INTO v_bank_fee_sum
  FROM card_swipes
  WHERE order_id = NEW.id;
  
  -- Tính toán profit
  SET NEW.profit = IFNULL(NEW.charge_customer_fee, 0) - v_bank_fee_sum;
  
  -- Tính toán need_to_swipe dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_swipe = NEW.total_amount - (NEW.total_swiped + IFNULL(NEW.negative_amount, 0));
  ELSE
    SET NEW.need_to_swipe = NEW.total_amount - NEW.total_swiped;
  END IF;

  -- Tính toán need_to_transfer dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_transfer = NEW.total_amount - NEW.total_transferred;
  ELSEIF NEW.order_type = 'Rút' THEN
    SET NEW.need_to_transfer = NEW.total_amount - IFNULL(NEW.charge_customer_fee, 0) - IFNULL(NEW.debt_deduction, 0) - NEW.total_transferred;
    SET NEW.negative_amount = 0;
  END IF;

  -- Tính toán total_fee cho tất cả loại đơn hàng
  SET NEW.total_fee = IFNULL(NEW.charge_customer_fee, 0) + IFNULL(NEW.negative_amount, 0);

  -- Xử lý khi thay đổi card_id hoặc charge_customer_fee
  -- Chỉ áp dụng cho đơn hàng "Đáo", không áp dụng cho đơn hàng "Rút"
  IF ((OLD.card_id IS NOT NULL AND OLD.card_id != NEW.card_id) OR
      (OLD.charge_customer_fee != NEW.charge_customer_fee) OR
      (OLD.total_fee != NEW.total_fee) OR
      (OLD.order_type != NEW.order_type)) AND
     (OLD.order_type = 'Đáo' OR NEW.order_type = 'Đáo') THEN

    -- Hoàn lại outstanding_fee cho customer cũ (chỉ khi đơn hàng cũ là "Đáo")
    IF OLD.card_id IS NOT NULL AND OLD.order_type = 'Đáo' AND OLD.total_fee > 0 THEN
      UPDATE customers c
      JOIN cards cd ON c.id = cd.customer_id
      SET c.outstanding_fee = c.outstanding_fee - OLD.total_fee
      WHERE cd.id = OLD.card_id;
    END IF;

    -- Cập nhật outstanding_fee cho customer mới (chỉ khi đơn hàng mới là "Đáo")
    IF NEW.card_id IS NOT NULL AND NEW.order_type = 'Đáo' AND NEW.total_fee > 0 THEN
      UPDATE customers c
      JOIN cards cd ON c.id = cd.customer_id
      SET c.outstanding_fee = c.outstanding_fee + NEW.total_fee
      WHERE cd.id = NEW.card_id;
    END IF;
  END IF;
  
  -- Kiểm tra outstanding_fee > 0 khi cập nhật đơn hàng "Rút" có giá trị "Trừ Nợ"
  IF NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 AND 
     (IFNULL(OLD.debt_deduction, 0) != IFNULL(NEW.debt_deduction, 0) OR OLD.card_id != NEW.card_id) THEN
    -- Lấy customer_id từ card_id
    SELECT customer_id INTO v_customer_id
    FROM cards
    WHERE id = NEW.card_id;
    
    -- Lấy outstanding_fee của customer
    SELECT outstanding_fee INTO v_outstanding_fee
    FROM customers
    WHERE id = v_customer_id;
    
    -- Kiểm tra outstanding_fee > 0
    IF v_outstanding_fee <= 0 THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Khách hàng không có nợ phí, không thể áp dụng Trừ Nợ';
    END IF;
    
    -- Kiểm tra debt_deduction <= (total_swiped - charge_customer_fee)
    IF NEW.debt_deduction > (NEW.total_swiped - IFNULL(NEW.charge_customer_fee, 0)) THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Số tiền Trừ Nợ không được vượt quá (Tổng đã quẹt - Tiền phí)';
    END IF;
    
    -- Xử lý khi debt_deduction thay đổi và đơn hàng đã ở trạng thái completed
    IF NEW.status = 'completed' AND OLD.status = 'completed' AND IFNULL(OLD.debt_deduction, 0) != IFNULL(NEW.debt_deduction, 0) THEN
      -- Tìm bản ghi money_returns hiện có
      SELECT id INTO v_money_return_id
      FROM money_returns
      WHERE order_id = NEW.id
      LIMIT 1;
      
      -- Nếu đã có bản ghi money_returns
      IF v_money_return_id IS NOT NULL THEN
        -- Nếu debt_deduction mới > 0
        IF IFNULL(NEW.debt_deduction, 0) > 0 THEN
          -- Cập nhật bản ghi money_returns hiện có
          UPDATE money_returns
          SET amount = NEW.debt_deduction,
              updated_at = NOW()
          WHERE id = v_money_return_id;
        -- Nếu debt_deduction mới = 0
        ELSE
          -- Xóa bản ghi money_returns hiện có
          DELETE FROM money_returns
          WHERE id = v_money_return_id;
        END IF;
      -- Nếu chưa có bản ghi money_returns nhưng debt_deduction mới > 0
      ELSEIF IFNULL(NEW.debt_deduction, 0) > 0 THEN
        -- Tạo bản ghi money_returns mới
        INSERT INTO money_returns (customer_id, amount, note, user_id, order_id, created_at, updated_at)
        VALUES (v_customer_id, NEW.debt_deduction, CONCAT('Trừ nợ từ đơn hàng Rút #', NEW.id), NEW.user_id, NEW.id, NOW(), NOW());
      END IF;
    END IF;
  END IF;
  
  -- Tự động cập nhật trạng thái đơn hàng dựa vào need_to_swipe và need_to_transfer
  IF NEW.order_type = 'Đáo' THEN
    IF NEW.need_to_swipe <= 0 AND NEW.need_to_transfer <= 0 THEN
      SET NEW.status = 'completed';
    ELSEIF NEW.total_swiped > 0 OR NEW.total_transferred > 0 THEN
      SET NEW.status = 'processing';
    END IF;
  ELSEIF NEW.order_type = 'Rút' THEN
    IF NEW.need_to_swipe <= 0 AND NEW.need_to_transfer <= 0 THEN
      SET NEW.status = 'completed';
    ELSEIF NEW.total_swiped > 0 OR NEW.total_transferred > 0 THEN
      SET NEW.status = 'processing';
    END IF;
  END IF;
END

CREATE TRIGGER `log_bank_fee_adjustments` AFTER UPDATE ON `card_swipes`
 FOR EACH ROW BEGIN
  -- Log khi bank_fee thay đổi mà amount không thay đổi
  -- (Điều này cho thấy có điều chỉnh thủ công)
  IF OLD.bank_fee != NEW.bank_fee AND OLD.amount = NEW.amount THEN
    INSERT INTO user_logs (user_id, action, entity_type, entity_id, details, created_at)
    VALUES (
      NEW.user_id,
      'UPDATE',
      'card_swipes',
      NEW.id,
      JSON_OBJECT(
        'action', 'manual_bank_fee_adjustment',
        'order_id', NEW.order_id,
        'old_bank_fee', OLD.bank_fee,
        'new_bank_fee', NEW.bank_fee,
        'old_net_amount', OLD.net_amount,
        'new_net_amount', NEW.net_amount,
        'bank_fee_difference', NEW.bank_fee - OLD.bank_fee,
        'timestamp', NOW()
      ),
      NOW()
    );
  END IF;
END

CREATE TRIGGER `log_manual_adjustments` AFTER UPDATE ON `orders`
 FOR EACH ROW BEGIN
  -- Log khi profit thay đổi mà không phải do trigger tự động
  -- (Điều này xảy ra khi có điều chỉnh thủ công)
  IF OLD.profit != NEW.profit AND OLD.charge_customer_fee = NEW.charge_customer_fee THEN
    INSERT INTO user_logs (user_id, action, entity_type, entity_id, details, created_at)
    VALUES (
      NEW.user_id,
      'UPDATE',
      'orders',
      NEW.id,
      JSON_OBJECT(
        'action', 'manual_profit_adjustment',
        'old_profit', OLD.profit,
        'new_profit', NEW.profit,
        'profit_difference', NEW.profit - OLD.profit,
        'timestamp', NOW()
      ),
      NOW()
    );
  END IF;
END