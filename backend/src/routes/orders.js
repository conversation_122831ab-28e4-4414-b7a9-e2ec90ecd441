const express = require('express');
const router = express.Router();
const { Order, Customer, Card, sequelize, Sequelize } = require('../models');
const { verifyToken } = require('../middleware/auth');
const Op = Sequelize.Op;

// L<PERSON>y danh sách tất cả đơn hàng
router.get('/', verifyToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Xử lý các tham số lọc
    const whereClause = {};

    // Lọc theo trạng thái
    if (req.query.status) {
      whereClause.status = req.query.status;
    }

    // Lọc theo loại đơn hàng
    if (req.query.order_type) {
      whereClause.order_type = req.query.order_type;
    }

    // Lọc theo khoảng thời gian
    if (req.query.start_date || req.query.end_date) {
      whereClause.created_at = {};

      if (req.query.start_date) {
        whereClause.created_at[Op.gte] = new Date(req.query.start_date);
      }

      if (req.query.end_date) {
        // Thêm 1 ngày vào end_date để bao gồm cả ngày kết thúc
        const endDate = new Date(req.query.end_date);
        endDate.setDate(endDate.getDate() + 1);
        whereClause.created_at[Op.lt] = endDate;
      }
    }

    // Xử lý lọc theo khách hàng
    let customerWhere = {};

    // Xử lý lọc theo ID khách hàng (chính xác hơn)
    if (req.query.customer_id) {
      customerWhere.id = req.query.customer_id;
    }
    // Xử lý lọc theo tên khách hàng (nếu không có ID)
    else if (req.query.customer_name && req.query.customer_name !== 'all') {
      customerWhere.name = {
        [Op.like]: `%${req.query.customer_name}%`
      };
    }

    // Khai báo biến searchTerm ở phạm vi rộng hơn để sử dụng ở nhiều nơi
    let searchTerm = '';
    let mainWhereClause = [];
    let cardWhere = {};
    // customerWhere đã được khai báo ở trên
    let customerRequired = req.query.customer_name || req.query.customer_id ? true : false;
    let cardRequired = customerRequired;

    // Xử lý tìm kiếm
    if (req.query.search) {
      searchTerm = req.query.search.toLowerCase();

      // Tạo điều kiện tìm kiếm chính
      // Sử dụng cách tiếp cận đơn giản hơn
      whereClause[Op.or] = [
        // Tìm kiếm theo ghi chú
        {
          note: {
            [Op.like]: `%${searchTerm}%`
          }
        }
        // Không tìm kiếm theo ID vì gây ra lỗi cú pháp
      ];

      try {
        // Tạo một truy vấn riêng biệt cho Card
        const cardIds = await Card.findAll({
          attributes: ['id'],
          where: {
            card_number: {
              [Op.like]: `%${searchTerm}%`
            }
          }
        }).then(cards => cards.map(card => card.id));

        // Nếu tìm thấy thẻ, thêm vào điều kiện tìm kiếm chính
        if (cardIds.length > 0) {
          whereClause[Op.or].push({
            card_id: {
              [Op.in]: cardIds
            }
          });
        }

        // Tạo một truy vấn riêng biệt cho Customer
        const customerIds = await Customer.findAll({
          attributes: ['id'],
          where: {
            name: {
              [Op.like]: `%${searchTerm}%`
            }
          }
        }).then(customers => customers.map(customer => customer.id));

        // Tìm các thẻ thuộc về các khách hàng đã tìm thấy
        if (customerIds.length > 0) {
          const cardIdsFromCustomers = await Card.findAll({
            attributes: ['id'],
            where: {
              customer_id: {
                [Op.in]: customerIds
              }
            }
          }).then(cards => cards.map(card => card.id));

          // Nếu tìm thấy thẻ, thêm vào điều kiện tìm kiếm chính
          if (cardIdsFromCustomers.length > 0) {
            whereClause[Op.or].push({
              card_id: {
                [Op.in]: cardIdsFromCustomers
              }
            });
          }
        }
      } catch (error) {
        console.error('Error in search processing:', error);
        // Nếu có lỗi, vẫn tiếp tục với điều kiện tìm kiếm cơ bản
      }
    }

    // Kiểm tra xem có điều kiện lọc theo khách hàng không
    const hasCustomerFilter = Object.keys(customerWhere).length > 0;

    // Tạo query object
    const queryOptions = {
      where: whereClause,
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id', 'customer_id'],
          required: hasCustomerFilter, // Bắt buộc có Card nếu đang lọc theo khách hàng
          include: [{
            model: Customer,
            where: hasCustomerFilter ? customerWhere : undefined, // Áp dụng điều kiện lọc khách hàng
            attributes: ['id', 'name', 'phone'],
            required: hasCustomerFilter // Bắt buộc có Customer nếu đang lọc theo khách hàng
          }]
        }
      ],
      attributes: {
        include: ['order_type', 'total_fee', 'charge_customer_fee', 'negative_amount', 'debt_deduction', 'profit']
      },
      order: [['created_at', 'DESC']],
      limit: limit,
      offset: offset
    };

    const { count, rows: orders } = await Order.findAndCountAll(queryOptions);

    const totalPages = Math.ceil(count / limit);

    // Format dữ liệu trước khi trả về
    const formattedOrders = orders.map(order => {
      // Đảm bảo các trường luôn có giá trị
      const cardData = order.Card || {};
      const customerData = (order.Card && order.Card.Customer) || {};

      return {
        id: order.id,
        card_id: order.card_id,
        order_date: order.created_at,
        total_amount: order.total_amount,
        fee_percentage: order.fee_percentage,
        note: order.note,
        status: order.status,
        order_type: order.get('order_type'),
        charge_customer_fee: order.get('charge_customer_fee'),
        negative_amount: order.get('negative_amount'),
        total_fee: order.get('total_fee'),
        profit: order.get('profit'),
        card_number: cardData.card_number || '',
        customer_name: customerData.name || '',
        created_at: order.created_at.toISOString(),
        updated_at: order.updated_at.toISOString(),
        customer: {
          id: customerData.id || 0,
          name: customerData.name || '',
          phone: customerData.phone || null
        },
        card: {
          id: cardData.id || 0,
          card_number: cardData.card_number || '',
          bank_id: cardData.bank_id || 0
        }
      };
    });

    res.json({
      success: true,
      data: formattedOrders,
      pagination: {
        total: count,
        page: page,
        totalPages: totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Lấy chi tiết đơn hàng
router.get('/:id', verifyToken, async (req, res) => {
  try {
    // Lấy dữ liệu từ database với quan hệ Card -> Customer
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id'],
          required: false, // Không bắt buộc phải có thẻ
          include: [{
            model: Customer,
            attributes: ['id', 'name', 'phone'],
            required: false // Không bắt buộc phải có khách hàng
          }]
        }
      ],
      attributes: {
        include: ['order_type', 'total_fee', 'charge_customer_fee', 'negative_amount', 'debt_deduction', 'profit', 'note', 'total_transferred', 'need_to_transfer']
      }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Đơn hàng không tồn tại'
      });
    }

    // Định dạng đơn hàng - Lấy thông tin card và customer từ quan hệ
    const orderData = {
      id: order.id,
      card_id: order.card_id,
      order_date: order.created_at,
      total_amount: parseFloat(order.total_amount),
      fee_percentage: parseFloat(order.fee_percentage),
      total_swiped: parseFloat(order.total_swiped),
      need_to_swipe: parseFloat(order.need_to_swipe),
      total_transferred: parseFloat(order.total_transferred || 0),
      need_to_transfer: parseFloat(order.need_to_transfer || 0),
      status: order.status,
      note: order.note,
      order_type: order.order_type,
      total_fee: parseFloat(order.total_fee || 0),
      profit: parseFloat(order.profit || 0),
      charge_customer_fee: parseFloat(order.charge_customer_fee || 0),
      negative_amount: parseFloat(order.negative_amount || 0),
      debt_deduction: parseFloat(order.debt_deduction || 0),
      created_at: order.created_at,
      updated_at: order.updated_at,
      customer: order.Card && order.Card.Customer ? {
        id: order.Card.Customer.id,
        name: order.Card.Customer.name,
        phone: order.Card.Customer.phone
      } : null,
      card: order.Card ? {
        id: order.Card.id,
        card_number: order.Card.card_number,
        bank_id: order.Card.bank_id
      } : null
    };

    res.json({
      success: true,
      data: orderData
    });
  } catch (error) {
    console.error('Error fetching order details:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể lấy chi tiết đơn hàng. Vui lòng thử lại sau.'
    });
  }
});

// Tạo đơn hàng mới
router.post('/', async (req, res) => {
  try {
    // Validate yêu cầu
    if (!req.body.card_id || !req.body.total_amount) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin cần thiết (card_id, total_amount)'
      });
    }

    // Kiểm tra xem card có tồn tại và lấy thông tin customer thông qua card
    const card = await Card.findByPk(req.body.card_id, {
      include: [{
        model: Customer,
        attributes: ['id', 'name', 'phone']
      }]
    });

    if (!card) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy thẻ'
      });
    }

    // Sử dụng raw SQL để thêm đơn hàng
    const orderSql = `
      INSERT INTO orders (
        card_id, order_type, total_amount, fee_percentage,
        total_swiped, need_to_swipe, total_transferred, need_to_transfer,
        total_fee, profit, charge_customer_fee,
        negative_amount, debt_deduction, status, note, user_id, created_at, updated_at
      ) VALUES (
        :card_id, :order_type, :total_amount, :fee_percentage,
        :total_swiped, :need_to_swipe, :total_transferred, :need_to_transfer,
        :total_fee, :profit, :charge_customer_fee,
        :negative_amount, :debt_deduction, :status, :note, :user_id, NOW(), NOW()
      )
    `;

    const orderResult = await sequelize.query(orderSql, {
      replacements: {
        card_id: req.body.card_id,
        order_type: req.body.order_type || 'Đáo',
        total_amount: req.body.total_amount,
        fee_percentage: req.body.fee_percentage || 1.7,
        total_swiped: req.body.total_swiped || 0,
        need_to_swipe: req.body.total_amount,
        total_transferred: req.body.total_transferred || 0,
        need_to_transfer: req.body.total_amount,
        total_fee: req.body.total_fee || 0,
        profit: req.body.profit || 0,
        charge_customer_fee: req.body.charge_customer_fee || 1,
        negative_amount: req.body.negative_amount || 0,
        debt_deduction: req.body.debt_deduction || 0,
        status: req.body.status || 'pending',
        note: req.body.note || '',
        user_id: req.body.user_id || 1
      },
      type: sequelize.QueryTypes.INSERT
    });

    // Xử lý kết quả
    const [insertId] = orderResult;
    if (insertId) {
      // Lấy thông tin đơn hàng vừa tạo
      const newOrder = await Order.findByPk(insertId, {
        include: [
          {
            model: Card,
            attributes: ['id', 'card_number', 'bank_id', 'customer_id'],
            include: [{
              model: Customer,
              attributes: ['id', 'name', 'phone']
            }]
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Tạo đơn hàng thành công',
        data: newOrder
      });
    } else {
      // Thử phương pháp dự phòng sử dụng Sequelize ORM nếu raw SQL thất bại
      const newOrder = await Order.create({
        card_id: req.body.card_id,
        order_type: req.body.order_type || 'Đáo',
        total_amount: req.body.total_amount,
        fee_percentage: req.body.fee_percentage || 1.7,
        total_swiped: req.body.total_swiped || 0,
        need_to_swipe: req.body.total_amount,
        total_transferred: req.body.total_transferred || 0,
        need_to_transfer: req.body.total_amount,
        total_fee: req.body.total_fee || 0,
        profit: req.body.profit || 0,
        charge_customer_fee: req.body.charge_customer_fee || 1,
        negative_amount: req.body.negative_amount || 0,
        debt_deduction: req.body.debt_deduction || 0,
        status: req.body.status || 'pending',
        note: req.body.note || '',
        user_id: req.body.user_id || 1
      });

      res.status(201).json({
        success: true,
        message: 'Tạo đơn hàng thành công (ORM)',
        data: newOrder
      });
    }
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi tạo đơn hàng',
      error: error.message
    });
  }
});

// Điều chỉnh lợi nhuận đơn hàng
router.put('/:id/adjust-profit', verifyToken, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Đơn hàng không tồn tại'
      });
    }

    const { new_profit, adjustment_reason } = req.body;

    // Validation
    if (new_profit === undefined || new_profit === null) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập lợi nhuận mới'
      });
    }

    if (!adjustment_reason || adjustment_reason.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập lý do điều chỉnh'
      });
    }

    const oldProfit = parseFloat(order.profit || 0);
    const newProfit = parseFloat(new_profit);

    // Tạo note điều chỉnh
    const adjustmentNote = `[Điều chỉnh lợi nhuận] ${adjustment_reason} - Từ ${oldProfit.toLocaleString('vi-VN')} thành ${newProfit.toLocaleString('vi-VN')}`;
    const updatedNote = order.note ? `${order.note}\n${adjustmentNote}` : adjustmentNote;

    // Cập nhật đơn hàng với lợi nhuận mới
    await order.update({
      profit: newProfit,
      note: updatedNote
    });

    // Log hoạt động
    const { logUserActivity } = require('../utils/userLogger');
    await logUserActivity(req.user.id, 'UPDATE', 'orders', order.id, {
      action: 'adjust_profit',
      old_profit: oldProfit,
      new_profit: newProfit,
      adjustment_reason: adjustment_reason,
      profit_difference: newProfit - oldProfit
    });

    // Lấy lại đơn hàng đã cập nhật với đầy đủ thông tin
    const updatedOrder = await Order.findByPk(req.params.id, {
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id', 'customer_id'],
          required: false,
          include: [{
            model: Customer,
            attributes: ['id', 'name', 'phone'],
            required: false
          }]
        }
      ],
      attributes: {
        include: ['order_type', 'total_fee', 'charge_customer_fee', 'negative_amount', 'debt_deduction', 'profit', 'note', 'total_transferred', 'need_to_transfer']
      }
    });

    res.json({
      success: true,
      message: 'Điều chỉnh lợi nhuận thành công',
      data: {
        adjustment_info: {
          id: updatedOrder.id,
          old_profit: oldProfit,
          new_profit: newProfit,
          profit_difference: newProfit - oldProfit,
          adjustment_reason: adjustment_reason,
          updated_at: updatedOrder.updated_at
        },
        order: updatedOrder
      }
    });

  } catch (error) {
    console.error('Error adjusting order profit:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể điều chỉnh lợi nhuận. Vui lòng thử lại sau.',
      error: error.message
    });
  }
});

// Cập nhật đơn hàng
router.put('/:id', verifyToken, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Đơn hàng không tồn tại'
      });
    }

    // Nếu có cập nhật card_id, kiểm tra card mới có tồn tại không
    if (req.body.card_id) {
      const card = await Card.findByPk(req.body.card_id, {
        include: [{
          model: Customer,
          attributes: ['id', 'name', 'phone']
        }]
      });

      if (!card) {
        return res.status(404).json({
          success: false,
          message: 'Không tìm thấy thẻ'
        });
      }
    }

    let { card_id, total_amount, fee_percentage, note, status, order_type, charge_customer_fee, negative_amount, debt_deduction } = req.body;

    // Cập nhật đơn hàng
    await order.update({
      card_id,
      total_amount,
      fee_percentage,
      note: note !== undefined ? note : order.note, // Xử lý đúng trường hợp note là chuỗi rỗng
      status,
      order_type,
      charge_customer_fee,
      negative_amount,
      debt_deduction
    });

    // Lấy lại đơn hàng với quan hệ
    const updatedOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id', 'customer_id'],
          include: [{
            model: Customer,
            attributes: ['id', 'name', 'phone']
          }]
        }
      ],
      attributes: {
        include: ['order_type', 'total_fee', 'charge_customer_fee', 'negative_amount', 'debt_deduction', 'profit', 'note', 'total_transferred', 'need_to_transfer']
      }
    });

    // Format dữ liệu trước khi trả về
    const formattedOrder = {
      id: updatedOrder.id,
      card_id: updatedOrder.card_id,
      order_date: updatedOrder.created_at,
      total_amount: updatedOrder.total_amount,
      fee_percentage: updatedOrder.fee_percentage,
      total_swiped: updatedOrder.total_swiped,
      need_to_swipe: updatedOrder.need_to_swipe,
      total_transferred: updatedOrder.total_transferred,
      need_to_transfer: updatedOrder.need_to_transfer,
      note: updatedOrder.note,
      status: updatedOrder.status,
      order_type: updatedOrder.get('order_type'),
      charge_customer_fee: updatedOrder.get('charge_customer_fee'),
      negative_amount: updatedOrder.get('negative_amount'),
      debt_deduction: updatedOrder.get('debt_deduction'),
      total_fee: updatedOrder.get('total_fee'),
      profit: updatedOrder.get('profit'),
      created_at: updatedOrder.created_at.toISOString(),
      updated_at: updatedOrder.updated_at.toISOString(),
      customer: updatedOrder.Card && updatedOrder.Card.Customer ? {
        id: updatedOrder.Card.Customer.id,
        name: updatedOrder.Card.Customer.name,
        phone: updatedOrder.Card.Customer.phone
      } : null,
      card: updatedOrder.Card ? {
        id: updatedOrder.Card.id,
        card_number: updatedOrder.Card.card_number,
        bank_id: updatedOrder.Card.bank_id
      } : null
    };

    res.json({
      success: true,
      data: formattedOrder
    });
  } catch (error) {
    console.error('Error updating order:', error);

    // Xử lý lỗi từ trigger
    if (error.name === 'SequelizeDatabaseError' && error.parent && error.parent.sqlState === '45000') {
      // Đây là lỗi từ trigger
      return res.status(400).json({
        success: false,
        message: error.parent.sqlMessage || 'Lỗi khi cập nhật đơn hàng',
        error: error.parent.sqlMessage
      });
    }

    // Các lỗi khác
    res.status(500).json({
      success: false,
      message: 'Không thể cập nhật đơn hàng. Vui lòng thử lại sau.',
      error: error.message
    });
  }
});

// Xóa đơn hàng
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Đơn hàng không tồn tại'
      });
    }


    // Xóa đơn hàng
    await order.destroy();

    res.json({
      success: true,
      message: 'Đơn hàng đã được xóa thành công'
    });
  } catch (error) {
    console.error('Error deleting order:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể xóa đơn hàng. Vui lòng thử lại sau.'
    });
  }
});

module.exports = router;
