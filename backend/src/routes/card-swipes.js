const express = require('express');
const router = express.Router();
const { Op } = require('sequelize'); // Import Op for operators if needed later
const CardSwipe = require('../models').CardSwipe;
const Card = require('../models').Card;
const Order = require('../models').Order;
const POS = require('../models').POS;
const Customer = require('../models').Customer;
const Bank = require('../models').Bank; // Import Bank model
const { sequelize } = require('../models');

// Get all card swipes with pagination and sorting
router.get('/', async (req, res) => {
  // Extract query parameters with defaults
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 20; // Default limit to 20
  const sort = req.query.sort || 'id'; // Default sort by id
  const order = req.query.order || 'DESC'; // Default order DESC (newest first)
  const posId = req.query.posId; // Get POS ID filter
  const startDate = req.query.startDate; // Get start date filter
  const endDate = req.query.endDate; // Get end date filter

  // Validate sort and order parameters (simple validation)
  const allowedSortColumns = ['id', 'swipe_date', 'amount', 'bank_fee', 'net_amount', 'order_id', 'pos_id', 'card_id'];
  const allowedOrderValues = ['ASC', 'DESC'];

  const sortColumn = allowedSortColumns.includes(sort) ? sort : 'id';
  const sortOrder = allowedOrderValues.includes(order.toUpperCase()) ? order.toUpperCase() : 'DESC';

  // Calculate offset
  const offset = (page - 1) * limit;

  try {
    // Build where clause for filters
    const whereClause = {};

    // Add POS filter if provided
    if (posId) {
      whereClause.pos_id = posId;
    }

    // Add date range filter if provided
    if (startDate || endDate) {
      whereClause.swipe_date = {};
      if (startDate) {
        whereClause.swipe_date[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.swipe_date[Op.lte] = new Date(endDate);
      }
    }

    // Use findAndCountAll for pagination
    const { count, rows } = await CardSwipe.findAndCountAll({
      where: whereClause,
      limit: limit,
      offset: offset,
      order: [[sortColumn, sortOrder]], // Apply sorting
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id'],
          include: [
            {
              model: Customer,
              attributes: ['id', 'name', 'phone']
            },
            {
              model: Bank, // Include Bank details within Card
              attributes: ['id', 'name'] // Specify attributes needed
            }
          ]
        },
        {
          model: Order,
          attributes: ['id', 'total_amount', 'fee_percentage']
        },
        {
          model: POS,
          attributes: ['id', 'name', 'balance', 'note']
        }
      ]
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    // Return paginated data
    res.json({
      success: true,
      data: rows,
      total: count,
      totalPages: totalPages,
      currentPage: page
    });

  } catch (error) {
    console.error("Error fetching card swipes:", error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get card swipes by order ID
router.get('/order/:orderId', async (req, res) => {
  try {
    const swipes = await CardSwipe.findAll({
      where: { order_id: req.params.orderId },
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id'],
          include: [
            {
              model: Customer,
              attributes: ['id', 'name', 'phone']
            }
          ]
        },
        {
          model: POS,
          attributes: ['id', 'name', 'balance', 'note']
        }
      ]
    });
    res.json({ success: true, data: swipes });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get card swipe by ID
router.get('/:id', async (req, res) => {
  try {
    const swipe = await CardSwipe.findByPk(req.params.id, {
      include: [
        {
          model: Card,
          attributes: ['id', 'card_number', 'bank_id'],
          include: [
            {
              model: Customer,
              attributes: ['id', 'name', 'phone']
            }
          ]
        },
        {
          model: Order,
          attributes: ['id', 'total_amount', 'fee_percentage']
        },
        {
          model: POS,
          attributes: ['id', 'name', 'balance', 'note']
        }
      ]
    });
    if (!swipe) {
      return res.status(404).json({ success: false, message: 'Card swipe not found' });
    }
    res.json({ success: true, data: swipe });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create new card swipe
router.post('/', async (req, res) => {
  try {
    const { order_id, card_id, swipe_date, amount, pos_id, bank_fee, net_amount, note, user_id } = req.body;

    // Calculate bank fee and net amount if not provided
    let bankFeeValue = bank_fee;
    let netAmountValue = net_amount;

    if (!bankFeeValue || !netAmountValue) {
      const card = await Card.findByPk(card_id);
      const terminal = await POS.findByPk(pos_id);
      if (!card) {
        return res.status(404).json({ success: false, message: 'Card not found' });
      }
      if (!terminal) {
        return res.status(404).json({ success: false, message: 'POS terminal not found' });
      }

      // Xác định phí dựa vào loại thẻ và ngân hàng
      let feeRate = 0;

      // Kiểm tra nếu thẻ và POS cùng ngân hàng
      if (card.bank_id && terminal.bank_id && card.bank_id === terminal.bank_id) {
        // Sử dụng phí của cùng ngân hàng
        feeRate = terminal.fee_bank || 0.5;
        console.log(`Áp dụng phí ngân hàng cùng hệ thống: ${feeRate}%`);
      } else {
        // Nếu không cùng ngân hàng, xác định loại thẻ dựa vào số đầu tiên
        const firstDigit = card.card_number.charAt(0);

        // Theo yêu cầu:
        // Số 4 sẽ là Visa
        // Số 5 sẽ là Master
        // Số 9 sẽ là Napas
        // Số 3 sẽ là JCB
        if (firstDigit === '4') {
          feeRate = terminal.visa_fee;
          console.log(`Áp dụng phí VISA: ${feeRate}%`);
        } else if (firstDigit === '5') {
          feeRate = terminal.master_fee;
          console.log(`Áp dụng phí Master: ${feeRate}%`);
        } else if (firstDigit === '9') {
          feeRate = terminal.napas_fee;
          console.log(`Áp dụng phí Napas: ${feeRate}%`);
        } else if (firstDigit === '3') {
          feeRate = terminal.jcb_fee;
          console.log(`Áp dụng phí JCB: ${feeRate}%`);
        } else {
          // Mặc định là Visa nếu không nhận dạng được
          feeRate = terminal.visa_fee;
          console.log(`Không nhận dạng được loại thẻ, áp dụng phí mặc định (Visa): ${feeRate}%`);
        }
      }

      bankFeeValue = (amount * feeRate) / 100;
      netAmountValue = amount - bankFeeValue;

      console.log(`Số tiền quẹt: ${amount}, Phí: ${feeRate}%, Phí ngân hàng: ${bankFeeValue}, Số tiền thực nhận: ${netAmountValue}`);
    }

    const newSwipe = await CardSwipe.create({
      order_id,
      card_id,
      swipe_date,
      amount,
      pos_id,
      bank_fee: bankFeeValue,
      net_amount: netAmountValue,
      note,
      user_id: user_id || 1
    });

    // Không cần cập nhật order vì đã có trigger after_card_swipe_insert xử lý
    // Trigger đã cập nhật total_swiped, need_to_swipe và profit
    const order = await Order.findByPk(order_id);
    if (!order) {
      console.error('Order not found for update:', order_id);
    }

    // Không cần cập nhật balance của terminal vì đã được xử lý bởi trigger after_card_swipe_insert

    res.status(201).json({ success: true, message: 'Card swipe created successfully', data: newSwipe });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error: ' + error.message });
  }
});

// Delete card swipe
router.delete('/:id', async (req, res) => {
  try {
    const swipe = await CardSwipe.findByPk(req.params.id);
    if (!swipe) {
      return res.status(404).json({ success: false, message: 'Card swipe not found' });
    }

    // Không cần cập nhật order vì đã có trigger after_card_swipe_delete xử lý
    // Trigger đã cập nhật total_swiped, need_to_swipe và profit
    const order = await Order.findByPk(swipe.order_id);
    if (!order) {
      console.error('Order not found for update:', swipe.order_id);
    }

    // Xóa bản ghi swipe
    await swipe.destroy();

    // Không cần cập nhật balance của terminal vì đã được xử lý bởi trigger after_card_swipe_delete

    res.json({ success: true, message: 'Card swipe deleted successfully' });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Điều chỉnh phí bank của card swipe
router.put('/:id/adjust-bank-fee', async (req, res) => {
  try {
    const swipe = await CardSwipe.findByPk(req.params.id, {
      include: [
        {
          model: Order,
          attributes: ['id', 'profit', 'charge_customer_fee']
        }
      ]
    });

    if (!swipe) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy giao dịch quẹt thẻ'
      });
    }

    const { new_bank_fee, adjustment_reason } = req.body;

    // Validation
    if (new_bank_fee === undefined || new_bank_fee === null) {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập phí bank mới'
      });
    }

    if (!adjustment_reason || adjustment_reason.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Vui lòng nhập lý do điều chỉnh'
      });
    }

    const oldBankFee = parseFloat(swipe.bank_fee || 0);
    const newBankFee = parseFloat(new_bank_fee);
    const swipeAmount = parseFloat(swipe.amount);

    // Tính toán net_amount mới
    const newNetAmount = swipeAmount - newBankFee;

    // Tạo note điều chỉnh
    const adjustmentNote = `[Điều chỉnh phí bank] ${adjustment_reason} - Từ ${oldBankFee.toLocaleString('vi-VN')}₫ thành ${newBankFee.toLocaleString('vi-VN')}₫`;
    const updatedNote = swipe.note ? `${swipe.note}\n${adjustmentNote}` : adjustmentNote;

    // Cập nhật card swipe
    await swipe.update({
      bank_fee: newBankFee,
      net_amount: newNetAmount,
      note: updatedNote
    });

    // Log hoạt động
    const { logUserActivity } = require('../utils/userLogger');
    await logUserActivity(req.user?.id || 1, 'UPDATE', 'card_swipes', swipe.id, {
      action: 'adjust_bank_fee',
      old_bank_fee: oldBankFee,
      new_bank_fee: newBankFee,
      old_net_amount: parseFloat(swipe.net_amount),
      new_net_amount: newNetAmount,
      adjustment_reason: adjustment_reason,
      bank_fee_difference: newBankFee - oldBankFee
    });

    // Lấy thông tin đơn hàng sau khi trigger đã cập nhật profit
    const updatedOrder = await Order.findByPk(swipe.order_id);

    res.json({
      success: true,
      message: 'Điều chỉnh phí bank thành công',
      data: {
        swipe_id: swipe.id,
        order_id: swipe.order_id,
        old_bank_fee: oldBankFee,
        new_bank_fee: newBankFee,
        old_net_amount: parseFloat(swipe.net_amount),
        new_net_amount: newNetAmount,
        bank_fee_difference: newBankFee - oldBankFee,
        adjustment_reason: adjustment_reason,
        updated_order_profit: updatedOrder ? parseFloat(updatedOrder.profit) : null
      }
    });

  } catch (error) {
    console.error('Error adjusting card swipe bank fee:', error);
    res.status(500).json({
      success: false,
      message: 'Không thể điều chỉnh phí bank. Vui lòng thử lại sau.',
      error: error.message
    });
  }
});

// Update card swipe
router.put('/:id', async (req, res) => {
  try {
    const swipe = await CardSwipe.findByPk(req.params.id);
    if (!swipe) {
      return res.status(404).json({ success: false, message: 'Card swipe not found' });
    }

    const {
      order_id, card_id, swipe_date, amount, pos_id,
      bank_fee, net_amount, note, user_id
    } = req.body;

    // Get the old values to calculate differences
    const oldAmount = swipe.amount;
    const oldNetAmount = swipe.net_amount;
    const oldPosId = swipe.pos_id;
    const oldOrderId = swipe.order_id;

    // Check if POS or amount changed
    const posChanged = oldPosId !== pos_id;
    const amountChanged = oldAmount !== amount;
    const orderChanged = oldOrderId !== order_id;

    // Recalculate bank fee and net amount if amount or POS changed
    let bankFeeValue = bank_fee;
    let netAmountValue = net_amount;

    if ((amountChanged || posChanged) && !bankFeeValue) {
      const card = await Card.findByPk(card_id || swipe.card_id);
      const terminal = await POS.findByPk(pos_id || swipe.pos_id);

      if (!card) {
        return res.status(404).json({ success: false, message: 'Card not found' });
      }
      if (!terminal) {
        return res.status(404).json({ success: false, message: 'POS terminal not found' });
      }

      // Xác định phí dựa vào loại thẻ và ngân hàng
      let feeRate = 0;

      // Kiểm tra nếu thẻ và POS cùng ngân hàng
      if (card.bank_id && terminal.bank_id && card.bank_id === terminal.bank_id) {
        // Sử dụng phí của cùng ngân hàng
        feeRate = terminal.fee_bank || 0.5;
        console.log(`Áp dụng phí ngân hàng cùng hệ thống: ${feeRate}%`);
      } else {
        // Nếu không cùng ngân hàng, xác định loại thẻ dựa vào số đầu tiên
        const firstDigit = card.card_number.charAt(0);

        // Theo yêu cầu:
        // Số 4 sẽ là Visa
        // Số 5 sẽ là Master
        // Số 9 sẽ là Napas
        // Số 3 sẽ là JCB
        if (firstDigit === '4') {
          feeRate = terminal.visa_fee;
          console.log(`Áp dụng phí VISA: ${feeRate}%`);
        } else if (firstDigit === '5') {
          feeRate = terminal.master_fee;
          console.log(`Áp dụng phí Master: ${feeRate}%`);
        } else if (firstDigit === '9') {
          feeRate = terminal.napas_fee;
          console.log(`Áp dụng phí Napas: ${feeRate}%`);
        } else if (firstDigit === '3') {
          feeRate = terminal.jcb_fee;
          console.log(`Áp dụng phí JCB: ${feeRate}%`);
        } else {
          // Mặc định là Visa nếu không nhận dạng được
          feeRate = terminal.visa_fee;
          console.log(`Không nhận dạng được loại thẻ, áp dụng phí mặc định (Visa): ${feeRate}%`);
        }
      }

      const newAmount = amount || swipe.amount;
      bankFeeValue = (newAmount * feeRate) / 100;
      netAmountValue = newAmount - bankFeeValue;

      console.log(`Số tiền quẹt: ${newAmount}, Phí: ${feeRate}%, Phí ngân hàng: ${bankFeeValue}, Số tiền thực nhận: ${netAmountValue}`);
    }

    // Update the swipe
    await swipe.update({
      order_id: order_id || swipe.order_id,
      card_id: card_id || swipe.card_id,
      swipe_date: swipe_date || swipe.swipe_date,
      amount: amount || swipe.amount,
      pos_id: pos_id || swipe.pos_id,
      bank_fee: bankFeeValue || swipe.bank_fee,
      net_amount: netAmountValue || swipe.net_amount,
      note: note !== undefined ? note : swipe.note,
      user_id: user_id || swipe.user_id
    });

    // Update related records if necessary
    if (amountChanged || orderChanged) {
      // If order changed, update both old and new orders
      if (orderChanged) {
        // Không cần cập nhật order vì đã có trigger after_card_swipe_delete và after_card_swipe_insert xử lý
        // Trigger đã cập nhật total_swiped, need_to_swipe và profit
      } else {
        // Không cần cập nhật order vì đã có trigger after_card_swipe_update xử lý
        // Trigger đã cập nhật total_swiped, need_to_swipe và profit
      }
    }

    // Không cần cập nhật balance của POS vì đã được xử lý bởi trigger after_card_swipe_update

    res.json({
      success: true,
      message: 'Card swipe updated successfully',
      data: swipe
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router;
